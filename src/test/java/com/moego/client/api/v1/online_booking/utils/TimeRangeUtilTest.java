package com.moego.client.api.v1.online_booking.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;

class TimeRangeUtilTest {

    @Test
    void getUnavailableDatesWithEmptyTimeRanges() {
        Map<LocalDate, List<DayTimeRangeDef>> dayTimeRanges = Map.of(
                LocalDate.of(2023, 1, 1), List.of(),
                LocalDate.of(2023, 1, 2), List.of());

        List<LocalDate> result = TimeRangeUtil.getUnavailableDates(dayTimeRanges);

        assertThat(result).containsExactlyInAnyOrder(LocalDate.of(2023, 1, 1), LocalDate.of(2023, 1, 2));
    }

    @Test
    void getUnavailableDatesWithNonEmptyTimeRanges() {
        Map<LocalDate, List<DayTimeRangeDef>> dayTimeRanges = Map.of(
                LocalDate.of(2023, 1, 1),
                        List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(9)
                                .setEndTime(10)
                                .build()),
                LocalDate.of(2023, 1, 2), List.of());

        List<LocalDate> result = TimeRangeUtil.getUnavailableDates(dayTimeRanges);

        assertThat(result).containsExactly(LocalDate.of(2023, 1, 2));
    }

    @Test
    void getUnavailableDatesWithMixedTimeRanges() {
        Map<LocalDate, List<DayTimeRangeDef>> dayTimeRanges = Map.of(
                LocalDate.of(2023, 1, 1),
                        List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(9)
                                .setEndTime(10)
                                .build()),
                LocalDate.of(2023, 1, 2), List.of(),
                LocalDate.of(2023, 1, 3),
                        List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(11)
                                .setEndTime(12)
                                .build()));

        List<LocalDate> result = TimeRangeUtil.getUnavailableDates(dayTimeRanges);

        assertThat(result).containsExactly(LocalDate.of(2023, 1, 2));
    }

    @Test
    void getUnavailableDatesWithAllNonEmptyTimeRanges() {
        Map<LocalDate, List<DayTimeRangeDef>> dayTimeRanges = Map.of(
                LocalDate.of(2023, 1, 1),
                        List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(9)
                                .setEndTime(10)
                                .build()),
                LocalDate.of(2023, 1, 2),
                        List.of(DayTimeRangeDef.newBuilder()
                                .setStartTime(11)
                                .setEndTime(12)
                                .build()));

        List<LocalDate> result = TimeRangeUtil.getUnavailableDates(dayTimeRanges);

        assertThat(result).isEmpty();
    }

    @Test
    void getUnavailableDatesWithEmptyMap() {
        Map<LocalDate, List<DayTimeRangeDef>> dayTimeRanges = Map.of();

        List<LocalDate> result = TimeRangeUtil.getUnavailableDates(dayTimeRanges);

        assertThat(result).isEmpty();
    }
}
