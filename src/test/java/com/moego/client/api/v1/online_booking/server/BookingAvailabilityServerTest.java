package com.moego.client.api.v1.online_booking.server;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.offering.v1.AdditionalServiceRule;
import com.moego.idl.models.offering.v1.CustomizedServiceCategoryView;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class BookingAvailabilityServerTest {

    @InjectMocks
    private BookingAvailabilityServer bookingAvailabilityServer;

    @Test
    void extractAdditionalServiceIds_shouldReturnEmptyList_whenCategoriesIsEmpty() {
        // given
        List<CustomizedServiceCategoryView> categories = List.of();

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnEmptyList_whenNoRulesAndNoBundleIds() {
        // given
        CustomizedServiceView service =
                CustomizedServiceView.newBuilder().setId(1L).build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnBundleServiceIds_whenOnlyBundleIdsExist() {
        // given
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L, 3L))
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(2).containsExactlyInAnyOrder(2L, 3L);
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnEmptyList_whenAdditionalServiceRuleDisabled() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(false)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(2L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).isEmpty();
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnServiceIds_whenAdditionalServiceRuleEnabled() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(2L)
                        .build())
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(3L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(2).containsExactlyInAnyOrder(2L, 3L);
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnCombinedIds_whenBothBundleAndRuleExist() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(3L)
                        .build())
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(4L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L, 3L))
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(3).containsExactlyInAnyOrder(2L, 3L, 4L);
    }

    @Test
    void extractAdditionalServiceIds_shouldReturnDistinctServiceIds_whenDuplicateServiceIds() {
        // given
        AdditionalServiceRule rule = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(2L)
                        .build())
                .build();
        CustomizedServiceView service = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L, 3L))
                .setAdditionalServiceRule(rule)
                .build();
        CustomizedServiceCategoryView category =
                CustomizedServiceCategoryView.newBuilder().addServices(service).build();
        List<CustomizedServiceCategoryView> categories = List.of(category);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(2).containsExactlyInAnyOrder(2L, 3L);
    }

    @Test
    void extractAdditionalServiceIds_shouldHandleMultipleCategories() {
        // given
        AdditionalServiceRule rule1 = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(3L)
                        .build())
                .build();
        AdditionalServiceRule rule2 = AdditionalServiceRule.newBuilder()
                .setEnable(true)
                .addApplyRules(AdditionalServiceRule.ApplyRule.newBuilder()
                        .setServiceId(4L)
                        .build())
                .build();
        CustomizedServiceView service1 = CustomizedServiceView.newBuilder()
                .setId(1L)
                .addAllBundleServiceIds(List.of(2L))
                .setAdditionalServiceRule(rule1)
                .build();
        CustomizedServiceView service2 = CustomizedServiceView.newBuilder()
                .setId(5L)
                .addAllBundleServiceIds(List.of(6L))
                .setAdditionalServiceRule(rule2)
                .build();
        CustomizedServiceCategoryView category1 =
                CustomizedServiceCategoryView.newBuilder().addServices(service1).build();
        CustomizedServiceCategoryView category2 =
                CustomizedServiceCategoryView.newBuilder().addServices(service2).build();
        List<CustomizedServiceCategoryView> categories = List.of(category1, category2);

        // when
        List<Long> result = bookingAvailabilityServer.extractAdditionalServiceIds(categories);

        // then
        assertThat(result).hasSize(4).containsExactlyInAnyOrder(2L, 3L, 4L, 6L);
    }
}
