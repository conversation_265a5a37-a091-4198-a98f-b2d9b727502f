package com.moego.client.api.v1.online_booking.utils;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.type.Date;
import com.moego.idl.service.online_booking.v1.QueryAvailableBookingDateRangeResponse;
import java.time.LocalDate;
import java.util.List;
import org.junit.jupiter.api.Test;

class AvailabilityUtilTest {

    @Test
    void getUnavailableDatesWithinAvailableRange() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 10);
        QueryAvailableBookingDateRangeResponse setting = QueryAvailableBookingDateRangeResponse.newBuilder()
                .setFromDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(1))
                .setToDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(10))
                .build();
        List<LocalDate> result = AvailabilityUtil.getOBUnavailableDates(startDate, endDate, setting);

        assertThat(result).isEmpty();
    }

    @Test
    void getUnavailableDatesOutsideAvailableRange() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 10);
        QueryAvailableBookingDateRangeResponse setting = QueryAvailableBookingDateRangeResponse.newBuilder()
                .setFromDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(5))
                .setToDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(8))
                .build();

        List<LocalDate> result = AvailabilityUtil.getOBUnavailableDates(startDate, endDate, setting);

        assertThat(result)
                .containsExactly(
                        LocalDate.of(2023, 1, 1),
                        LocalDate.of(2023, 1, 2),
                        LocalDate.of(2023, 1, 3),
                        LocalDate.of(2023, 1, 4),
                        LocalDate.of(2023, 1, 9),
                        LocalDate.of(2023, 1, 10));
    }

    @Test
    void getUnavailableDatesWithPartialOverlap() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 10);
        QueryAvailableBookingDateRangeResponse setting = QueryAvailableBookingDateRangeResponse.newBuilder()
                .setFromDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(3))
                .setToDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(7))
                .build();

        List<LocalDate> result = AvailabilityUtil.getOBUnavailableDates(startDate, endDate, setting);

        assertThat(result)
                .containsExactly(
                        LocalDate.of(2023, 1, 1),
                        LocalDate.of(2023, 1, 2),
                        LocalDate.of(2023, 1, 8),
                        LocalDate.of(2023, 1, 9),
                        LocalDate.of(2023, 1, 10));
    }

    @Test
    void getUnavailableDatesWithNoOverlap() {
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        LocalDate endDate = LocalDate.of(2023, 1, 10);

        QueryAvailableBookingDateRangeResponse setting = QueryAvailableBookingDateRangeResponse.newBuilder()
                .setFromDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(11))
                .setToDate(Date.newBuilder().setYear(2023).setMonth(1).setDay(20))
                .build();
        List<LocalDate> result = AvailabilityUtil.getOBUnavailableDates(startDate, endDate, setting);

        assertThat(result)
                .containsExactly(
                        LocalDate.of(2023, 1, 1),
                        LocalDate.of(2023, 1, 2),
                        LocalDate.of(2023, 1, 3),
                        LocalDate.of(2023, 1, 4),
                        LocalDate.of(2023, 1, 5),
                        LocalDate.of(2023, 1, 6),
                        LocalDate.of(2023, 1, 7),
                        LocalDate.of(2023, 1, 8),
                        LocalDate.of(2023, 1, 9),
                        LocalDate.of(2023, 1, 10));
    }
}
