package com.moego.client.api.v1.payment.controller;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.grooming.mapper.BookOnlineMapper;
import com.moego.client.api.v1.payment.mapper.PrepayMapper;
import com.moego.common.dto.PaymentSummary;
import com.moego.common.enums.PaymentMethodEnum;
import com.moego.idl.client.payment.v1.CreatePaymentIntentRequest;
import com.moego.idl.client.payment.v1.CreatePaymentIntentResponse;
import com.moego.idl.client.payment.v1.GetPrepayAmountRequest;
import com.moego.idl.client.payment.v1.GetPrepayAmountResponse;
import com.moego.idl.client.payment.v1.PrepayRequest;
import com.moego.idl.client.payment.v1.PrepayResponse;
import com.moego.idl.client.payment.v1.PrepayServiceGrpc;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.client.IOnlineBookingClient;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.PrepayAmountParams;
import com.moego.server.payment.client.IPaymentPrepayClient;
import com.moego.server.payment.dto.PaymentIntentDTO;
import com.moego.server.payment.params.BookOnlinePayParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@GrpcService
@RequiredArgsConstructor
public class PrepayController extends PrepayServiceGrpc.PrepayServiceImplBase {

    private final PrepayMapper prepayMapper;
    private final BookOnlineMapper bookOnlineMapper;
    private final CustomerService customerService;
    private final IOnlineBookingClient onlineBookingClient;
    private final IPaymentPrepayClient prepayClient;
    private final ICustomerCustomerClient customerClient;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getPrepayAmount(
            GetPrepayAmountRequest request, StreamObserver<GetPrepayAmountResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        PrepayAmountParams params = new PrepayAmountParams();
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(linkCustomerId);
        params.setCustomerData(customerParams);
        params.setPetData(bookOnlineMapper.defToParam(request.getSelectedPetServicesList()));
        params.setDiscountCode(request.getDiscountCode());
        PrepayAmountDTO dto = onlineBookingClient.getPrepayAmount(businessId, params);

        responseObserver.onNext(GetPrepayAmountResponse.newBuilder()
                .setPrepay(prepayMapper.dtoToDef(dto))
                .addAllServiceCharges(prepayMapper.applyServiceChargeDTOToView(dto.getApplyServiceChargeList()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void createPaymentIntent(
            CreatePaymentIntentRequest request, StreamObserver<CreatePaymentIntentResponse> responseObserver) {
        PaymentIntentDTO dto = prepayClient.createPaymentIntent(Math.toIntExact(request.getBusinessId()));

        responseObserver.onNext(CreatePaymentIntentResponse.newBuilder()
                .setPaymentIntentId(dto.getPaymentIntendId())
                .setSecretKey(dto.getSecretKey())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void prepay(PrepayRequest request, StreamObserver<PrepayResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        MoeBusinessCustomerDTO customer = customerClient.getCustomerWithDeleted(linkCustomerId);
        PrepayAmountDTO prepayAmountDTO = onlineBookingClient.getPrepayAmountByGuid(
                businessId, request.getPrepay().getPrepayGuid());
        if (Objects.isNull(prepayAmountDTO)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        BookOnlinePayParams params = new BookOnlinePayParams();
        params.setModule(PaymentMethodEnum.MODULE_GROOMING);
        params.setGuid(request.getPrepay().getPrepayGuid());
        params.setCustomerId(linkCustomerId);
        BigDecimal subTotal = Optional.ofNullable(prepayAmountDTO.getSubTotal()).orElse(BigDecimal.ZERO);
        BigDecimal taxAmount =
                Optional.ofNullable(prepayAmountDTO.getTaxAmount()).orElse(BigDecimal.ZERO);
        BigDecimal serviceChargeAmount =
                Optional.ofNullable(prepayAmountDTO.getServiceChargeAmount()).orElse(BigDecimal.ZERO);
        BigDecimal bookingFee = Optional.ofNullable(prepayAmountDTO.getFee()).orElse(BigDecimal.ZERO);
        BigDecimal tipsAmount = BigDecimal.valueOf(request.getPrepay().getTipsAmount());
        BigDecimal discountAmount =
                Optional.ofNullable(prepayAmountDTO.getDiscountAmount()).orElse(BigDecimal.ZERO);
        params.setAmount(subTotal.add(taxAmount)
                .add(serviceChargeAmount)
                .add(bookingFee)
                .add(tipsAmount)
                .subtract(discountAmount));
        params.setTipsAmount(tipsAmount);
        params.setPaidBy(customer.getFirstName() + " " + customer.getLastName());
        params.setBookingFeeAmount(bookingFee);
        params.setStripePaymentMethod((byte) request.getStripePaymentMethod().getNumber());
        params.setPaymentIntentId(request.getPaymentIntentId());
        params.setSaveCard(request.getIsSaveCard());

        PaymentSummary.PaymentDto dto = prepayClient.prepay(businessId, params);
        responseObserver.onNext(PrepayResponse.newBuilder()
                .setTotalPayment(dto.getAmount().doubleValue())
                .build());
        responseObserver.onCompleted();
    }
}
