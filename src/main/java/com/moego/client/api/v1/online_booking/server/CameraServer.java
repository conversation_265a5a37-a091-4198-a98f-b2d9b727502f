package com.moego.client.api.v1.online_booking.server;

import com.moego.client.api.v1.appointment.utils.AppointmentUtil;
import com.moego.client.api.v1.enterprise.service.CompanyService;
import com.moego.client.api.v1.online_booking.converter.LodgingUnitConverter;
import com.moego.client.api.v1.online_booking.service.AppointmentService;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.client.online_booking.v1.CameraServiceGrpc;
import com.moego.idl.client.online_booking.v1.GetCameraListParams;
import com.moego.idl.client.online_booking.v1.GetCameraListResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.organization.v1.CameraFilter;
import com.moego.idl.models.organization.v1.CameraModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.VisibilityType;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.organization.v1.GetCameraListRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class CameraServer extends CameraServiceGrpc.CameraServiceImplBase {

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final com.moego.idl.service.organization.v1.CameraServiceGrpc.CameraServiceBlockingStub
            cameraServiceBlockingStub;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitServiceBlockingStub;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final CompanyService companyService;
    private final AppointmentService appointmentService;
    private final FeatureFlagApi featureFlagApi;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getCameraList(GetCameraListParams request, StreamObserver<GetCameraListResult> responseObserver) {
        var customerId = AuthContext.get().customerId();
        // query business info
        OBBusinessDTO dto = onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        // query camera model
        var cameraList = queryCameraList(dto.getCompanyId(), dto.getBusinessId());
        if (CollectionUtils.isEmpty(cameraList)) {
            responseObserver.onNext(GetCameraListResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // add public camera to response && convert to map
        Map<Long, CameraModel> cameraModelMap =
                cameraList.stream().collect(Collectors.toMap(CameraModel::getId, Function.identity()));

        // query appt
        var appointmentList =
                queryOBInProgressAppt(dto.getCompanyId(), dto.getBusinessId().longValue(), customerId);

        if (CollectionUtils.isEmpty(appointmentList)) {
            // 没有预约，不返回任何 camera list
            responseObserver.onNext(GetCameraListResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // filter public camera
        List<GetCameraListResult.CameraView> cameraViews = cameraList.stream()
                .filter(camera -> VisibilityType.PUBLIC == camera.getVisibilityType())
                .map(camera -> GetCameraListResult.CameraView.newBuilder()
                        .setCamera(camera)
                        .build())
                .toList();

        var responseBuilder = GetCameraListResult.newBuilder();
        responseBuilder.addAllCameras(cameraViews);

        // query lodging unit list
        var lodgingUnitList = queryLodgingIdForInProgressAppt(
                dto.getCompanyId(),
                appointmentList.stream().map(AppointmentModel::getId).collect(Collectors.toList()));

        // add private camera to list
        lodgingUnitList.stream()
                .filter(lodgingUnit -> cameraModelMap.containsKey(lodgingUnit.getCameraId()))
                .forEach(lodgingUnit -> {
                    var camera = cameraModelMap.get(lodgingUnit.getCameraId());
                    var builder = GetCameraListResult.CameraView.newBuilder().setCamera(camera);
                    if (camera.getVisibilityType() == VisibilityType.PRIVATE) {
                        builder.setLodgingUnit(LodgingUnitConverter.INSTANCE.toLodgingUnitView(lodgingUnit));
                    }
                    responseBuilder.addCameras(builder.build());
                });

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    private List<CameraModel> queryCameraList(Long companyId, Integer businessId) {
        return cameraServiceBlockingStub
                .getCameraList(GetCameraListRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .setCameraFilter(CameraFilter.newBuilder()
                                .setIsActive(true)
                                .setRelationBusinessId(businessId)
                                .build())
                        .build())
                .getCamerasList();
    }

    private List<LodgingUnitModel> queryLodgingIdForInProgressAppt(Long companyId, List<Long> appointmentIds) {
        // query pet detail( lodging unit)
        var petDetailList =
                appointmentService.listAllPetDetails(companyId, appointmentIds).key();
        if (CollectionUtils.isEmpty(petDetailList)) {
            return List.of();
        }

        var unitIds = petDetailList.stream()
                .map(PetDetailModel::getLodgingId)
                .filter(id -> id > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(unitIds)) {
            return List.of();
        }

        // query lodging unit model
        return lodgingUnitServiceBlockingStub
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllUnitIds(unitIds)
                        .build())
                .getLodgingUnitListList();
    }

    private List<AppointmentModel> queryOBInProgressAppt(Long companyId, Long businessId, Long customerId) {
        var zoneId = companyService.getZoneId(companyId);
        var filter = AppointmentUtil.buildFilterForInProgress(customerId, zoneId).toBuilder()
                .setIncludeBlock(false)
                .setIsWaitingList(false)
                .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.BOARDING))
                .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.DAYCARE))
                .addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.EVALUATION));

        var addGroomingType = featureFlagApi.isOn(
                FeatureFlags.CAMERA_OB_GROOMING_IS_SHOW,
                FeatureFlagContext.builder().company(companyId).build());
        if (addGroomingType) {
            filter.addAllServiceTypeIncludes(ServiceItemEnum.getBitValueListByServiceItem(ServiceItemEnum.GROOMING));
        }

        // 去重
        var distinctServiceTypeIncludes = new HashSet<>(filter.getServiceTypeIncludesList());
        filter.clearServiceTypeIncludes().addAllServiceTypeIncludes(distinctServiceTypeIncludes);

        // query customer in progress appointments
        return appointmentServiceBlockingStub
                .listAppointments(ListAppointmentsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addBusinessIds(businessId)
                        .setFilter(filter.build())
                        .build())
                .getAppointmentsList();
    }
}
