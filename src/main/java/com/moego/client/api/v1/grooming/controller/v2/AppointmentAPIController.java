package com.moego.client.api.v1.grooming.controller.v2;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.client.api.v1.shared.helper.AppointmentHelper;
import com.moego.idl.client.online_booking.v2.AppointmentAPIGrpc;
import com.moego.idl.client.online_booking.v2.UpdateAppointmentParams;
import com.moego.idl.client.online_booking.v2.UpdateAppointmentResult;
import com.moego.idl.models.activity_log.v1.Resource;
import com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.service.activity_log.v1.CreateActivityLogRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v2.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v2.UpdateAppointmentRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.enums.AppointmentAction;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.message.api.INotificationService;
import com.moego.server.message.params.notification.NotificationClientUpdateAppointmentParams;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Controller;

/**
 * <AUTHOR>
 * @since 2025/6/13
 */
@Controller
@RequiredArgsConstructor
public class AppointmentAPIController extends AppointmentAPIGrpc.AppointmentAPIImplBase {

    private final AppointmentHelper appointmentHelper;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailStub;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final ActivityLogServiceGrpc.ActivityLogServiceBlockingStub activityLogStub;
    private final INotificationService notificationApi;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updateAppointment(
            UpdateAppointmentParams request, StreamObserver<UpdateAppointmentResult> responseObserver) {

        var biz = onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        var builder = UpdateAppointmentRequest.newBuilder();

        builder.setCompanyId(biz.getCompanyId());
        builder.setBusinessId(biz.getBusinessId());
        builder.setAppointmentId(request.getAppointmentId());
        if (request.hasAppointment()) {
            builder.setAppointment(request.getAppointment());
        }
        builder.addAllPetDetails(request.getPetDetailsList());

        appointmentStub.updateAppointment(builder.build());

        ThreadPool.execute(() -> recordActivityLogForUpdateAppointment(request));

        ThreadPool.execute(() -> sendNotificationForUpdateAppointment(request));

        responseObserver.onNext(UpdateAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void sendNotificationForUpdateAppointment(UpdateAppointmentParams request) {
        var appointmentId = request.getAppointmentId();
        var appointment = appointmentHelper.mustGetAppointment(appointmentId);

        var staffIds = getRelatedStaffIds(appointmentId);

        var params = new NotificationClientUpdateAppointmentParams();
        params.setBusinessId(Math.toIntExact(appointment.getBusinessId()));
        params.setStaffIdList(staffIds.stream().map(Long::intValue).collect(Collectors.toSet()));

        notificationApi.sendClientUpdateAppointmentNotification(params);
    }

    private List<Long> getRelatedStaffIds(long appointmentId) {
        var resp = petDetailStub.getPetDetailList(GetPetDetailListRequest.newBuilder()
                .addAppointmentIds(appointmentId)
                .build());

        var petDetails = resp.getPetDetailsList();
        var evaluations = resp.getPetEvaluationsList();

        var staffIds = new ArrayList<Long>();

        for (var petDetail : petDetails) {
            if (isNormal(petDetail.getStaffId())) {
                staffIds.add(petDetail.getStaffId());
            }
        }

        for (var evaluation : evaluations) {
            if (isNormal(evaluation.getStaffId())) {
                staffIds.add(evaluation.getStaffId());
            }
        }

        return staffIds;
    }

    private void recordActivityLogForUpdateAppointment(UpdateAppointmentParams request) {
        var appointment = appointmentHelper.mustGetAppointment(request.getAppointmentId());

        activityLogStub.createActivityLog(CreateActivityLogRequest.newBuilder()
                .setCompanyId(appointment.getCompanyId())
                .setBusinessId(appointment.getBusinessId())
                .setIsRoot(true)
                .setAction(AppointmentAction.CLIENT_UPDATE_APPOINTMENT)
                .setResourceType(Resource.Type.APPOINTMENT)
                .setResourceId(String.valueOf(appointment.getId()))
                .setDetails(JsonUtil.toJson(request))
                .build());
    }
}
