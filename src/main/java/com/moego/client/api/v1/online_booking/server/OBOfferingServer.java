package com.moego.client.api.v1.online_booking.server;

import com.moego.client.api.v1.online_booking.utils.PricingRuleUtil;
import com.moego.client.api.v1.shared.helper.OfferingHelper;
import com.moego.idl.client.online_booking.v1.GetApplicableOfferingsRequest;
import com.moego.idl.client.online_booking.v1.GetApplicableOfferingsResponse;
import com.moego.idl.client.online_booking.v1.ListPricingRuleParams;
import com.moego.idl.client.online_booking.v1.ListPricingRuleResult;
import com.moego.idl.client.online_booking.v1.OBOfferingServiceGrpc;
import com.moego.idl.models.grooming.v1.ShowBasePrice;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceApplicableFilter;
import com.moego.idl.models.offering.v1.ServiceFilterByService;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.ListPricingRuleFilter;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.online_booking.v1.OBOfferingCategoryView;
import com.moego.idl.models.online_booking.v1.OBOfferingServiceView;
import com.moego.idl.service.offering.v1.GetApplicableServiceListRequest;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v2.ListPricingRulesRequest;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.utils.PageUtil;
import com.moego.lib.utils.model.Pair;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.api.IOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.grooming.params.ob.ServiceOBSettingQueryParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class OBOfferingServer extends OBOfferingServiceGrpc.OBOfferingServiceImplBase {
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final IOnlineBookingService onlineBookingService;
    private final com.moego.idl.service.offering.v2.PricingRuleServiceGrpc.PricingRuleServiceBlockingStub
            pricingRuleServiceBlockingStub;
    private final OfferingHelper offeringHelper;
    private final BookingCareTypeServiceGrpc.BookingCareTypeServiceBlockingStub bookingCareTypeService;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getApplicableOfferings(
            GetApplicableOfferingsRequest request, StreamObserver<GetApplicableOfferingsResponse> responseObserver) {
        OBBusinessDTO biz = onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        // get booking care type
        var bookingCareType = bookingCareTypeService
            .getBookingCareType(GetBookingCareTypeRequest.newBuilder()
                .setId(request.getCareTypeId())
                .setBusinessId(biz.getBusinessId())
                .setCompanyId(biz.getCompanyId())
                .build())
            .getBookingCareType();

        // query applicable services
        var serviceCategories = serviceManagementService
                .getApplicableServiceList(GetApplicableServiceListRequest.newBuilder()
                        .setBusinessId(biz.getBusinessId())
                        .setCompanyId(biz.getCompanyId())
                        .setServiceItemType(request.getServiceItemType())
                        .setServiceType(ServiceType.SERVICE)
                        .setInactive(false)
                        .setOnlyAvailable(true)
                        .build())
                .getCategoryListList();
        List<Integer> serviceIds;
        if (bookingCareType.getIsAllServiceApplicable()) {
            serviceIds = serviceCategories.stream()
                .flatMap(category -> category.getServicesList().stream())
                .map(CustomizedServiceView::getId)
                .map(Long::intValue)
                .toList();
        } else if (bookingCareType.getIsAllServiceApplicable()) {
        }


        // query applicable add-ons
        var addOnCategories = serviceManagementService
                .getApplicableServiceList(GetApplicableServiceListRequest.newBuilder()
                        .setBusinessId(biz.getBusinessId())
                        .setCompanyId(biz.getCompanyId())
                        .setServiceItemType(request.getServiceItemType())
                        .setServiceType(ServiceType.ADDON)
                        .setInactive(false)
                        .setOnlyAvailable(true)
                        .setFilter(ServiceApplicableFilter.newBuilder()
                                .setFilterByService(ServiceFilterByService.newBuilder()
                                        .setServiceItemType(request.getServiceItemType())
                                        .build())
                                .build())
                        .build())
                .getCategoryListList();
        var addOnIds = addOnCategories.stream()
                .flatMap(category -> category.getServicesList().stream())
                .map(CustomizedServiceView::getId)
                .map(Long::intValue)
                .toList();

        var serviceOBSettingMap = onlineBookingService.getServiceOBSetting(new ServiceOBSettingQueryParams(
                biz.getCompanyId(),
                biz.getBusinessId().longValue(),
                Stream.concat(serviceIds.stream(), addOnIds.stream()).collect(Collectors.toList())));
        var serviceCategoriesWithOBSetting = serviceCategories.stream()
                .map(category -> OBOfferingCategoryView.newBuilder()
                        .setCategoryId(category.getCategoryId())
                        .setName(category.getName())
                        .addAllServices(category.getServicesList().stream()
                                .filter(service -> serviceOBSettingMap.containsKey(service.getId())
                                        && serviceOBSettingMap
                                                .get(service.getId())
                                                .bookOnlineAvailable())
                                .map(service -> OBOfferingServiceView.newBuilder()
                                        .setServiceBasicInfo(service)
                                        .setShowPrice(ShowBasePrice.forNumber(serviceOBSettingMap
                                                .get(service.getId())
                                                .showBasePrice()))
                                        .build())
                                .toList())
                        .build())
                .toList();

        var addOnCategoriesWithOBSetting = addOnCategories.stream()
                .map(category -> OBOfferingCategoryView.newBuilder()
                        .setCategoryId(category.getCategoryId())
                        .setName(category.getName())
                        .addAllServices(category.getServicesList().stream()
                                .filter(service -> serviceOBSettingMap.containsKey(service.getId())
                                        && serviceOBSettingMap
                                                .get(service.getId())
                                                .bookOnlineAvailable())
                                .map(service -> OBOfferingServiceView.newBuilder()
                                        .setServiceBasicInfo(service)
                                        .setShowPrice(ShowBasePrice.forNumber(serviceOBSettingMap
                                                .get(service.getId())
                                                .showBasePrice()))
                                        .build())
                                .toList())
                        .build())
                .toList();

        responseObserver.onNext(GetApplicableOfferingsResponse.newBuilder()
                .addAllServiceCategories(serviceCategoriesWithOBSetting)
                .addAllAddonCategories(addOnCategoriesWithOBSetting)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void listPricingRule(ListPricingRuleParams request, StreamObserver<ListPricingRuleResult> responseObserver) {
        OBBusinessDTO biz = onlineBookingApi.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        var filterBuilder = ListPricingRuleFilter.newBuilder();
        if (request.hasFilter()) {
            var filter = request.getFilter();
            if (filter.hasIsActive()) {
                filterBuilder.setIsActive(filter.getIsActive());
            }
            if (filter.hasServiceItemType()) {
                filterBuilder.addCareTypes(filter.getServiceItemType());
            }
        }

        List<PricingRule> pricingRules = pricingRuleServiceBlockingStub
                .listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setFilter(filterBuilder.build())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .setCompanyId(biz.getCompanyId())
                        .build())
                .getPricingRulesList();
        if (CollectionUtils.isEmpty(pricingRules)) {
            responseObserver.onNext(ListPricingRuleResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // filter pricing rules by date range or date list
        List<PricingRule> result = pricingRules;
        if (request.hasDateRange()) {
            result = pricingRules.stream()
                    .map(rule -> PricingRuleUtil.processPeakDateRule(rule, request.getDateRange()))
                    .filter(Objects::nonNull)
                    .toList();
        } else if (request.hasDateList()) {
            result = pricingRules.stream()
                    .map(rule -> PricingRuleUtil.processPeakDateRule(rule, request.getDateList()))
                    .filter(Objects::nonNull)
                    .toList();
        }

        List<ServiceModel> servicesList = PageUtil.fetchAll((pageNum, pageSize) -> {
            var response = serviceManagementService.listService(ListServiceRequest.newBuilder()
                    .setTokenCompanyId(biz.getCompanyId())
                    .addAllServiceItemTypes(List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE))
                    .setPagination(PaginationRequest.newBuilder()
                            .setPageNum(pageNum)
                            .setPageSize(pageSize)
                            .build())
                    .build());
            return Pair.of(response.getServicesList(), response.getPagination().getTotal());
        });

        responseObserver.onNext(ListPricingRuleResult.newBuilder()
                .addAllPricingRulesV2(result)
                .addAllServices(servicesList)
                .build());
        responseObserver.onCompleted();
    }
}
