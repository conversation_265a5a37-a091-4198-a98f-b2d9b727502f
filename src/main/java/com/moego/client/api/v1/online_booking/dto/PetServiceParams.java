package com.moego.client.api.v1.online_booking.dto;

import jakarta.annotation.Nullable;
import java.time.LocalDate;
import java.util.List;
import lombok.Data;

@Data
public class PetServiceParams {
    private List<EvaluationService> services;

    @Data
    public static class EvaluationService {
        private long evaluationId;

        @Nullable
        private LocalDate date;

        @Nullable
        private Integer time; // optional
    }
}
