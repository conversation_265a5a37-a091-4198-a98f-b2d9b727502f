package com.moego.client.api.v1.online_booking.utils;

import com.moego.client.api.v1.converter.DateConverter;
import com.moego.idl.service.online_booking.v1.QueryAvailableBookingDateRangeResponse;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Stream;

public class AvailabilityUtil {
    public static List<LocalDate> getOBUnavailableDates(
            LocalDate startDate, LocalDate endDate, QueryAvailableBookingDateRangeResponse setting) {
        LocalDate obAvailableDateFrom = DateConverter.INSTANCE.toLocalDate(setting.getFromDate());
        LocalDate obAvailableDateTo = DateConverter.INSTANCE.toLocalDate(setting.getToDate());
        return Stream.iterate(startDate, date -> !date.isAfter(endDate), date -> date.plusDays(1))
                .filter(date -> date.isBefore(obAvailableDateFrom) || date.isAfter(obAvailableDateTo))
                .toList();
    }
}
