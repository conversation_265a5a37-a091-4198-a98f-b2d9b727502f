package com.moego.client.api.v1.business_customer;

import com.moego.idl.client.business_customer.v1.BusinessPetFeedingScheduleServiceGrpc;
import com.moego.idl.client.business_customer.v1.ListPetFeedingScheduleParams;
import com.moego.idl.client.business_customer.v1.ListPetFeedingScheduleResult;
import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleView;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleSettingModel;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class BusinessPetFeedingScheduleServer
        extends BusinessPetFeedingScheduleServiceGrpc.BusinessPetFeedingScheduleServiceImplBase {
    private final IGroomingOnlineBookingService onlineBookingService;
    private final com.moego.idl.service.business_customer.v1.BusinessPetFeedingScheduleServiceGrpc
                    .BusinessPetFeedingScheduleServiceBlockingStub
            petFeedingScheduleServiceBlockingStub;
    private final BusinessPetScheduleConverter petFeedingScheduleConverter;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void listPetFeedingSchedule(
            ListPetFeedingScheduleParams request, StreamObserver<ListPetFeedingScheduleResult> responseObserver) {
        OBBusinessDTO dto = onlineBookingService.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        ListPetFeedingScheduleResponse feedingSchedules =
                petFeedingScheduleServiceBlockingStub.listPetFeedingSchedule(ListPetFeedingScheduleRequest.newBuilder()
                        .setCompanyId(dto.getCompanyId())
                        .addAllPetIds(request.getPetIdsList())
                        .build());

        Map<Long, List<BusinessPetScheduleSettingModel>> feedingSchedulesMap =
                feedingSchedules.getSchedulesList().stream()
                        .collect(Collectors.groupingBy(BusinessPetScheduleSettingModel::getScheduleId));

        // 按 petId 分组
        Map<Long, List<BusinessPetFeedingScheduleView>> petIdToFeedingSchedulesMap =
                feedingSchedules.getFeedingsList().stream()
                        .map(feedingModel -> petFeedingScheduleConverter.feedingScheduleToView(
                                feedingModel, feedingSchedulesMap.getOrDefault(feedingModel.getId(), List.of())))
                        .collect(Collectors.groupingBy(BusinessPetFeedingScheduleView::getPetId));

        List<ListPetFeedingScheduleResult.PetFeedingSchedule> petFeedingSchedules = request.getPetIdsList().stream()
                .map(petId -> {
                    var petFeedingSchedule = petIdToFeedingSchedulesMap.getOrDefault(petId, List.of());
                    return ListPetFeedingScheduleResult.PetFeedingSchedule.newBuilder()
                            .setPetId(petId)
                            .addAllFeedingSchedules(petFeedingSchedule)
                            .build();
                })
                .toList();

        responseObserver.onNext(ListPetFeedingScheduleResult.newBuilder()
                .addAllPetFeedingSchedules(petFeedingSchedules)
                .build());
        responseObserver.onCompleted();
    }
}
