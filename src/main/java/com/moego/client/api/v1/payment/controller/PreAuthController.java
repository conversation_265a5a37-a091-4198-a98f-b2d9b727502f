package com.moego.client.api.v1.payment.controller;

import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.grooming.mapper.BookOnlineMapper;
import com.moego.client.api.v1.payment.mapper.PrepayMapper;
import com.moego.idl.client.payment.v1.GetPreAuthAmountRequest;
import com.moego.idl.client.payment.v1.GetPreAuthAmountResponse;
import com.moego.idl.client.payment.v1.PreAuthServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.client.IOnlineBookingClient;
import com.moego.server.grooming.dto.PreAuthAmountDTO;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.PreAuthAmountParams;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/10/8
 */
@GrpcService
@RequiredArgsConstructor
public class PreAuthController extends PreAuthServiceGrpc.PreAuthServiceImplBase {

    private final BookOnlineMapper bookOnlineMapper;
    private final PrepayMapper prepayMapper;
    private final CustomerService customerService;
    private final IOnlineBookingClient onlineBookingClient;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getPreAuthAmount(
            GetPreAuthAmountRequest request, StreamObserver<GetPreAuthAmountResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        PreAuthAmountParams params = new PreAuthAmountParams();
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(linkCustomerId);
        params.setCustomerData(customerParams);
        params.setPetData(bookOnlineMapper.defToParam(request.getSelectedPetServicesList()));
        params.setDiscountCode(request.getDiscountCode());
        PreAuthAmountDTO dto = onlineBookingClient.getPreAuthAmount(businessId, params);

        responseObserver.onNext(GetPreAuthAmountResponse.newBuilder()
                .setPreAuth(prepayMapper.dtoToDef(dto))
                .addAllServiceCharges(prepayMapper.applyServiceChargeDTOToView(dto.getApplyServiceChargeList()))
                .build());
        responseObserver.onCompleted();
    }
}
