package com.moego.client.api.v1.online_booking.converter;

import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.lib.common.util.JsonUtil;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        imports = {JsonUtil.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BookingCareTypeConverter {

    BookingCareTypeConverter INSTANCE = Mappers.getMapper(BookingCareTypeConverter.class);

    default ListBookingCareTypesRequest toListRequest(long businessId, Long companyId, Long staffId) {
        return ListBookingCareTypesRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStaffId(staffId)
                .build();
    }
}
