package com.moego.client.api.v1.online_booking.utils;

import com.moego.client.api.v1.converter.DateConverter;
import com.moego.idl.models.offering.v2.Condition;
import com.moego.idl.models.offering.v2.ConditionGroup;
import com.moego.idl.models.offering.v2.ConditionType;
import com.moego.idl.models.offering.v2.GenericValue;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.offering.v2.PricingRuleConfiguration;
import com.moego.idl.models.offering.v2.RepeatDates;
import com.moego.idl.models.offering.v2.RuleType;
import com.moego.idl.models.online_booking.v1.ServiceDateListDef;
import com.moego.idl.models.online_booking.v1.ServiceDateRangeDef;
import com.moego.idl.utils.v2.StringDateRange;
import jakarta.annotation.Nullable;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;
import org.springframework.util.CollectionUtils;

@UtilityClass
public class PricingRuleUtil {

    /**
     * Process pricing rule for a date range.
     *
     * @param rule      The pricing rule model to process
     * @param dateRange The service date range
     * @return Updated pricing rule model
     */
    @Nullable
    public static PricingRule processPeakDateRule(PricingRule rule, ServiceDateRangeDef dateRange) {
        if (nonPeakDateRule(rule)) {
            return null;
        }

        var dates = DateConverter.INSTANCE
                .toLocalDate(dateRange.getStartDate())
                .datesUntil(DateConverter.INSTANCE.toLocalDate(dateRange.getEndDate()))
                .toList();

        return processRule(rule, dates);
    }

    /**
     * Process pricing rule for a list of dates.
     *
     * @param rule     The pricing rule model to process
     * @param dateList The service date list
     * @return Updated pricing rule model
     */
    @Nullable
    public static PricingRule processPeakDateRule(PricingRule rule, ServiceDateListDef dateList) {
        if (nonPeakDateRule(rule) || dateList.getDatesList().isEmpty()) {
            return null;
        }

        List<LocalDate> dates = dateList.getDatesList().stream()
                .map(DateConverter.INSTANCE::toLocalDate)
                .toList();

        return processRule(rule, dates);
    }

    private static boolean nonPeakDateRule(PricingRule rule) {
        return !Objects.equals(RuleType.PEAK_DATE, rule.getType());
    }

    private static PricingRule processRule(PricingRule rule, List<LocalDate> dates) {
        var conditions = extractConditions(rule);

        var validPeakDates = conditions.stream()
                .filter(peakDate -> isDateInRange(peakDate.getValue(), dates))
                .toList();

        return buildUpdatedPricingRule(rule, validPeakDates);
    }

    private static List<Condition> extractConditions(PricingRule rule) {
        return rule.getRuleConfiguration().getConditionGroupsList().stream()
                .map(ConditionGroup::getConditionsList)
                .flatMap(Collection::stream)
                .filter(condition -> Objects.equals(ConditionType.DATE_RANGE, condition.getType())
                        || Objects.equals(ConditionType.REPEAT_DATES, condition.getType()))
                .toList();
    }

    private static boolean isDateInRange(GenericValue value, List<LocalDate> dates) {
        return switch (value.getValueCase()) {
            case DATE_RANGE -> {
                var dateRange = value.getDateRange();
                yield isDateInRange(dateRange, dates);
            }
            case REPEAT_DATES -> {
                var repeatDates = value.getRepeatDates();
                yield isDateInRange(repeatDates, dates);
            }
            default -> false;
        };
    }

    private static boolean isDateInRange(StringDateRange peakDate, List<LocalDate> dates) {
        DateRange peakDateRange =
                new DateRange(LocalDate.parse(peakDate.getStartDate()), LocalDate.parse(peakDate.getEndDate()));

        return dates.stream().anyMatch(peakDateRange::contains);
    }

    private static boolean isDateInRange(RepeatDates repeatDates, List<LocalDate> dates) {
        if (!repeatDates.hasDateRange() || CollectionUtils.isEmpty(dates)) {
            return false;
        }

        var startDate = LocalDate.parse(repeatDates.getDateRange().getStartDate());
        var endDate = LocalDate.parse(repeatDates.getDateRange().getEndDate());

        for (LocalDate date : dates) {
            if (date.isBefore(startDate) || date.isAfter(endDate)) {
                return false;
            }

            if (checkWeeklyRule(date, startDate, repeatDates.getWeek())) {
                return true;
            }
        }

        return false;
    }

    private static boolean checkWeeklyRule(LocalDate targetDate, LocalDate startDate, RepeatDates.Week weekRule) {
        if (weekRule.getIntervalNum() <= 0) {
            return false;
        }

        var targetDayOfWeek = targetDate.getDayOfWeek();
        var allowedDays = weekRule.getDayOfWeeksList().stream()
                .map(d -> DayOfWeek.of(d.getNumber()))
                .collect(Collectors.toSet());

        if (!allowedDays.contains(targetDayOfWeek)) {
            return false;
        }

        long daysDiff = ChronoUnit.DAYS.between(startDate, targetDate);
        long weeksDiff = daysDiff / 7;

        if (weeksDiff % weekRule.getIntervalNum() == 0) {
            return true;
        }

        long cycleWeeks = (weeksDiff / weekRule.getIntervalNum()) * weekRule.getIntervalNum();
        LocalDate cycleStartDate = startDate.plusWeeks(cycleWeeks);
        LocalDate cycleEndDate = cycleStartDate.plusDays(6);

        return !targetDate.isBefore(cycleStartDate) && !targetDate.isAfter(cycleEndDate);
    }

    @Nullable
    private static PricingRule buildUpdatedPricingRule(PricingRule rule, List<Condition> validPeakDates) {
        if (CollectionUtils.isEmpty(validPeakDates)) {
            return null;
        }

        PricingRuleConfiguration updatedConfig = PricingRuleConfiguration.newBuilder()
                .addConditionGroups(ConditionGroup.newBuilder()
                        .addAllConditions(validPeakDates)
                        .setEffect(rule.getRuleConfiguration()
                                .getConditionGroups(0)
                                .getEffect())
                        .build())
                .build();

        return rule.toBuilder().setRuleConfiguration(updatedConfig).build();
    }

    /**
     * Helper class to encapsulate date range logic
     */
    private static class DateRange {
        private final LocalDate startDate;
        private final LocalDate endDate;

        DateRange(LocalDate startDate, LocalDate endDate) {
            this.startDate = startDate;
            this.endDate = endDate;
        }

        boolean contains(LocalDate date) {
            return !date.isBefore(startDate) && !date.isAfter(endDate);
        }

        boolean overlaps(DateRange other) {
            return !startDate.isAfter(other.endDate) && !endDate.isBefore(other.startDate);
        }
    }
}
