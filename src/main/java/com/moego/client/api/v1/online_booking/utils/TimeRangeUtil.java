package com.moego.client.api.v1.online_booking.utils;

import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;

public class TimeRangeUtil {

    public static List<LocalDate> getUnavailableDates(Map<LocalDate, List<DayTimeRangeDef>> dayTimeRanges) {
        return dayTimeRanges.entrySet().stream()
                .filter(k -> CollectionUtils.isEmpty(k.getValue()))
                .map(Map.Entry::getKey)
                .toList();
    }
}
