package com.moego.client.api.v1.payment.mapper;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.online_booking.v1.CardOnFileDef;
import com.moego.idl.models.online_booking.v1.PreAuthDef;
import com.moego.idl.models.online_booking.v1.PrepayDef;
import com.moego.idl.models.order.v1.ServiceChargeOnlineBookingView;
import com.moego.server.grooming.dto.ApplyServiceChargeDTO;
import com.moego.server.grooming.dto.PreAuthAmountDTO;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.dto.ServiceChargeDTO;
import com.moego.server.grooming.params.ob.CardOnFileParams;
import com.moego.server.grooming.params.ob.PreAuthDetailParams;
import com.moego.server.grooming.params.ob.PrepayDetailParams;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/10/9
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN)
public interface PrepayMapper {
    @Mappings({
        @Mapping(target = "prepayGuid", source = "guid"),
        @Mapping(target = "subtotal", source = "subTotal"),
        @Mapping(target = "bookingFee", source = "fee"),
        @Mapping(target = "processingFee", source = "initProcessingFee"),
    })
    PrepayDef dtoToDef(PrepayAmountDTO dto);

    @Mappings({@Mapping(target = "processingFee", source = "initProcessingFee")})
    PreAuthDef dtoToDef(PreAuthAmountDTO dto);

    ServiceChargeOnlineBookingView dtoToView(ServiceChargeDTO dto);

    List<ServiceChargeOnlineBookingView> dtoToView(List<ServiceChargeDTO> dtoList);

    @Mapping(target = "hasCard", source = "isHasCard")
    CardOnFileParams defToParams(CardOnFileDef def);

    PreAuthDetailParams defToParams(PreAuthDef def);

    PrepayDetailParams defToParams(PrepayDef def);

    default List<ServiceChargeOnlineBookingView> applyServiceChargeDTOToView(List<ApplyServiceChargeDTO> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return List.of();
        }

        return dtoList.stream()
                .filter(PrepayMapper::filterApplyServiceChargeDTO)
                .map(dto -> {
                    var serviceChargeDTO = dto.getServiceChargeDTO();
                    ServiceChargeOnlineBookingView serviceChargeOnlineBookingView = dtoToView(serviceChargeDTO);
                    return serviceChargeOnlineBookingView.toBuilder()
                            .setApplyQuantity(dto.getApplyQuantity())
                            .setTotalPrice(serviceChargeDTO
                                    .getPrice()
                                    .multiply(BigDecimal.valueOf(dto.getApplyQuantity()))
                                    .doubleValue())
                            .build();
                })
                .toList();
    }

    // 计算 ApplyServiceChargeDTO total price 前置条件
    private static Boolean filterApplyServiceChargeDTO(ApplyServiceChargeDTO dto) {
        return Objects.nonNull(dto.getServiceChargeDTO())
                && CommonUtil.isNormal(dto.getApplyQuantity())
                && CommonUtil.isNormal(dto.getServiceChargeDTO().getPrice());
    }
}
