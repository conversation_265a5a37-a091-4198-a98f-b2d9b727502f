package com.moego.client.api.v1.business_customer;

import com.moego.idl.models.business_customer.v1.BusinessPetFeedingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleView;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationModel;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleView;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleSettingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/1/18
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BusinessPetScheduleConverter {

    @Mapping(target = ".", source = "model")
    BusinessPetFeedingScheduleView modelToView(BusinessPetFeedingModel model);

    @Mapping(target = ".", source = "model")
    BusinessPetMedicationScheduleView modelToView(BusinessPetMedicationModel model);

    default BusinessPetFeedingScheduleView feedingScheduleToView(
            BusinessPetFeedingModel model, List<BusinessPetScheduleSettingModel> schedules) {
        if (CollectionUtils.isEmpty(schedules)) {
            schedules = List.of();
        }

        BusinessPetFeedingScheduleView businessPetFeedingScheduleView = modelToView(model);
        return businessPetFeedingScheduleView.toBuilder()
                .addAllFeedingTimes(schedules.stream()
                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                .setScheduleTime(time.getScheduleTime())
                                .putAllExtraJson(time.getScheduleExtraJsonMap())
                                .build())
                        .toList())
                .build();
    }
}
