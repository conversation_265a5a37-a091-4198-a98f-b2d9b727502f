package com.moego.client.api.v1.online_booking.converter;

import com.moego.client.api.v1.converter.DateConverter;
import com.moego.client.api.v1.online_booking.dto.PetServiceParams;
import com.moego.client.api.v1.shared.util.ProtobufUtil;
import com.moego.idl.client.online_booking.v1.GetAvailableDatesParams;
import com.moego.idl.client.online_booking.v1.GetAvailableTimeRangesParams;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.service.online_booking.v1.GetAvailableDateTimeResponse;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface BookingAvailabilityConverter {
    BookingAvailabilityConverter INSTANCE = Mappers.getMapper(BookingAvailabilityConverter.class);

    default Map<LocalDate, List<DayTimeRangeDef>> convertToDateTimeMap(
            List<GetAvailableDateTimeResponse.DateTimeRange> dateTimeRanges) {
        if (CollectionUtils.isEmpty(dateTimeRanges)) {
            return Map.of();
        }
        Map<LocalDate, List<DayTimeRangeDef>> dateTimeMap = new HashMap<>();
        for (GetAvailableDateTimeResponse.DateTimeRange range : dateTimeRanges) {
            LocalDate date = DateConverter.INSTANCE.toLocalDate(range.getDate());
            List<DayTimeRangeDef> timeRanges = range.getTimeRangeList();
            dateTimeMap.put(date, timeRanges);
        }
        return dateTimeMap;
    }

    default List<PetServiceParams> convertFromTimeRangeProtoList(
            List<GetAvailableTimeRangesParams.PetServices> protoList) {
        return protoList.stream()
                .map(proto -> {
                    PetServiceParams param = new PetServiceParams();

                    List<PetServiceParams.EvaluationService> evaluations = proto.getServicesList().stream()
                            .filter(GetAvailableTimeRangesParams.PetServices.Service::hasEvaluation)
                            .map(service -> {
                                var evaluation = service.getEvaluation();
                                PetServiceParams.EvaluationService eval = new PetServiceParams.EvaluationService();
                                eval.setEvaluationId(evaluation.getEvaluationId());

                                if (evaluation.hasDate()) {
                                    eval.setDate(ProtobufUtil.toLocalDate(evaluation.getDate()));
                                }
                                if (evaluation.hasTime()) {
                                    eval.setTime(evaluation.getTime());
                                }
                                return eval;
                            })
                            .toList();

                    param.setServices(evaluations);
                    return param;
                })
                .toList();
    }

    default List<PetServiceParams> convertFromDateProtoList(List<GetAvailableDatesParams.PetServices> protoList) {
        return protoList.stream()
                .map(proto -> {
                    PetServiceParams param = new PetServiceParams();

                    List<PetServiceParams.EvaluationService> evaluations = proto.getServicesList().stream()
                            .filter(GetAvailableDatesParams.PetServices.Service::hasEvaluation)
                            .map(service -> {
                                var evaluation = service.getEvaluation();
                                PetServiceParams.EvaluationService eval = new PetServiceParams.EvaluationService();
                                eval.setEvaluationId(evaluation.getEvaluationId());

                                if (evaluation.hasDate()) {
                                    eval.setDate(ProtobufUtil.toLocalDate(evaluation.getDate()));
                                }
                                if (evaluation.hasTime()) {
                                    eval.setTime(evaluation.getTime());
                                }
                                return eval;
                            })
                            .toList();

                    param.setServices(evaluations);
                    return param;
                })
                .toList();
    }
}
