package com.moego.client.api.v1.online_booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.backend.proto.customer.v1.Customer;
import com.moego.backend.proto.customer.v1.CustomerServiceGrpc;
import com.moego.backend.proto.customer.v1.GetCustomerRequest;
import com.moego.client.api.v1.online_booking.converter.PetServiceDetailConverter;
import com.moego.client.api.v1.online_booking.dto.DepositOrderParams;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.client.online_booking.v1.PetServices;
import com.moego.idl.client.online_booking.v1.SubmitBookingRequestParams;
import com.moego.idl.models.online_booking.v1.Pet;
import com.moego.idl.models.online_booking.v1.PetServiceDetails;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.PreviewBookingRequestPricingRequest;
import com.moego.idl.service.order.v1.CreateDepositOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v2.DepositRuleServiceGrpc;
import com.moego.idl.service.order.v2.PreviewDepositOrderRequest;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class DepositService {
    private final DepositRuleServiceGrpc.DepositRuleServiceBlockingStub depositRuleService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderService;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestService;
    private final CustomerServiceGrpc.CustomerServiceBlockingStub moegoCustomerService;

    public OrderDetailModelV1 createDepositOrder(long bookingRequestId, OrderDetailModelV1 depositOrderPreview) {
        var depositOrder = depositOrderPreview.getOrder();
        var createDepositRequest = CreateDepositOrderRequest.newBuilder()
                .setCompanyId(depositOrder.getCompanyId())
                .setBusinessId(depositOrder.getBusinessId())
                .setCustomerId(depositOrder.getCustomerId())
                .setSourceType(OrderSourceType.BOOKING_REQUEST)
                .setSourceId(bookingRequestId)
                .setDepositAmount(depositOrder.getTotalAmount())
                .setDepositDescription(depositOrder.getDescription())
                .build();
        return orderService.createDepositOrder(createDepositRequest).getOrder();
    }

    public DepositOrderParams getDepositOrderParams(
            OBBusinessDTO biz, Long customerIdFromAuth, SubmitBookingRequestParams request) {
        var depositOrderPreview = previewDepositOrderByRule(
                biz.getCompanyId(), biz.getBusinessId(), customerIdFromAuth, request.getPetServicesList());
        if (depositOrderPreview != null) {
            return new DepositOrderParams(depositOrderPreview);
        }

        // 如果前端传了 preAuth，则要使用 Deposit 构建
        if (request.hasPreAuth()) {
            var preAuth = request.getPreAuth();
            // deposit 单只认 total，这里要将金额累计起来
            var amount = BigDecimal.valueOf(preAuth.getServiceTotal())
                    .add(BigDecimal.valueOf(preAuth.getTaxAmount()))
                    .add(BigDecimal.valueOf(preAuth.getTipsAmount()))
                    .add(BigDecimal.valueOf(preAuth.getServiceChargeAmount()));
            return new DepositOrderParams(OrderDetailModelV1.newBuilder()
                    .setOrder(OrderModelV1.newBuilder()
                            .setCompanyId(biz.getCompanyId())
                            .setBusinessId(biz.getBusinessId())
                            .setCustomerId(
                                    Optional.ofNullable(customerIdFromAuth).orElse(0L))
                            .setTotalAmount(MoneyUtils.toGoogleMoney(amount, "USD"))
                            .build())
                    .build());
        }

        return new DepositOrderParams(null);
    }

    @Nullable
    private OrderDetailModelV1 previewDepositOrderByRule(
            long companyId, long businessId, Long customerIdFromAuth, List<PetServices> petServices) {
        if (petServices.isEmpty()) {
            return null;
        }

        var previewPricingRequestBuilder = PreviewBookingRequestPricingRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllPetServices(
                        generateVirtualIdForNewPets(PetServiceDetailConverter.INSTANCE.toModels(petServices)));
        if (customerIdFromAuth != null) {
            previewPricingRequestBuilder.setCustomerId(customerIdFromAuth);
        }
        var previewPricingResponse =
                bookingRequestService.previewBookingRequestPricing(previewPricingRequestBuilder.build());
        var servicePricingDetails = previewPricingResponse.getLineItemsList().stream()
                .map(it -> PreviewDepositOrderRequest.ServicePricingDetail.newBuilder()
                        .setPetId(it.getPetId())
                        .setService(it.getService())
                        .setUnitPrice(it.getUnitPrice())
                        .setQuantity(it.getQuantity())
                        .setTotalPrice(it.getTotalPrice())
                        .build())
                .toList();

        var previewDepositRequestBuilder = PreviewDepositOrderRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllServicePricingDetails(servicePricingDetails);
        var customerId = checkAndGetCustomerId(customerIdFromAuth);
        if (customerId != null) {
            previewDepositRequestBuilder.setCustomerId(customerId);
        }
        var startDate = findStartDate(petServices);
        if (startDate != null) {
            previewDepositRequestBuilder.setAppointmentStartDate(startDate);
        }
        var previewResponse = depositRuleService.previewDepositOrder(previewDepositRequestBuilder.build());

        if (previewResponse.hasDepositOrderDetailPreview()) {
            return previewResponse.getDepositOrderDetailPreview();
        }
        return null;
    }

    /**
     * See {@link CashierService#generateVirtualIdForNewPets(List)}.
     */
    private List<PetServiceDetails> generateVirtualIdForNewPets(List<PetServiceDetails> petServices) {
        var nextVirtualId = petServices.stream()
                        .mapToLong(petService -> petService.getPet().getPetId())
                        .filter(CommonUtil::isNormal)
                        .max()
                        .orElse(0)
                + 1;

        var idCounter = new AtomicLong(nextVirtualId);

        return petServices.stream()
                .map(pss -> {
                    if (pss.getIsNewPet()) {
                        return pss.toBuilder()
                                .mergePet(Pet.newBuilder()
                                        .setPetId(idCounter.getAndIncrement())
                                        .build())
                                .build();
                    } else {
                        return pss;
                    }
                })
                .toList();
    }

    /**
     * 获取 customer id，如果是 new visitor 或者 lead，则返回 null
     */
    @Nullable
    private Long checkAndGetCustomerId(@Nullable Long customerIdFromAuth) {
        if (!isNormal(customerIdFromAuth)) {
            return null;
        }

        var customer = moegoCustomerService.getCustomer(GetCustomerRequest.newBuilder()
                .setCustomerId(customerIdFromAuth)
                .build());
        // Lead 也当做 new visitor
        if (Customer.Type.LEAD.equals(customer.getType())) {
            return null;
        }

        return customerIdFromAuth;
    }

    /**
     * 从 PetServices 列表里找到最早的 start date 作为 preview deposit 时的 start date
     */
    private String findStartDate(List<PetServices> petServices) {
        return petServices.stream()
                .flatMap(ps -> ps.getServicesList().stream().flatMap(service -> switch (service.getServiceCase()) {
                    case GROOMING -> Stream.of(service.getGrooming().getStartDate());
                    case BOARDING -> Stream.of(service.getBoarding().getStartDate());
                    case DAYCARE -> service.getDaycare().getDatesList().stream();
                        // 其他类型现在都不支持，所以直接返回 null
                    default -> null;
                }))
                // 所有 date 都是 YYYY-MM-DD 格式，所以直接字典序就能找到最早的
                .sorted()
                .findFirst()
                .orElse(null);
    }
}
