// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/service/online_booking/v1/ob_available_date_time_service.proto

package onlinebookingsvcpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	v11 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Get available date time request
type GetAvailableDateTimeRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// from date
	FromDate *date.Date `protobuf:"bytes,3,opt,name=from_date,json=fromDate,proto3" json:"from_date,omitempty"`
	// to date
	ToDate *date.Date `protobuf:"bytes,4,opt,name=to_date,json=toDate,proto3" json:"to_date,omitempty"`
	// business id
	ServiceItemType v1.ServiceItemType `protobuf:"varint,5,opt,name=service_item_type,json=serviceItemType,proto3,enum=moego.models.offering.v1.ServiceItemType" json:"service_item_type,omitempty"`
	// service/evaluation ids 用来 filter capacity
	RelationServiceIds []int64 `protobuf:"varint,6,rep,packed,name=relation_service_ids,json=relationServiceIds,proto3" json:"relation_service_ids,omitempty"`
}

func (x *GetAvailableDateTimeRequest) Reset() {
	*x = GetAvailableDateTimeRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDateTimeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDateTimeRequest) ProtoMessage() {}

func (x *GetAvailableDateTimeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDateTimeRequest.ProtoReflect.Descriptor instead.
func (*GetAvailableDateTimeRequest) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescGZIP(), []int{0}
}

func (x *GetAvailableDateTimeRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *GetAvailableDateTimeRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *GetAvailableDateTimeRequest) GetFromDate() *date.Date {
	if x != nil {
		return x.FromDate
	}
	return nil
}

func (x *GetAvailableDateTimeRequest) GetToDate() *date.Date {
	if x != nil {
		return x.ToDate
	}
	return nil
}

func (x *GetAvailableDateTimeRequest) GetServiceItemType() v1.ServiceItemType {
	if x != nil {
		return x.ServiceItemType
	}
	return v1.ServiceItemType(0)
}

func (x *GetAvailableDateTimeRequest) GetRelationServiceIds() []int64 {
	if x != nil {
		return x.RelationServiceIds
	}
	return nil
}

// get available date time response
type GetAvailableDateTimeResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// arrival available time range
	ArrivalTimeRange []*GetAvailableDateTimeResponse_DateTimeRange `protobuf:"bytes,1,rep,name=arrival_time_range,json=arrivalTimeRange,proto3" json:"arrival_time_range,omitempty"`
	// pick up available time range
	PickUpTimeRange []*GetAvailableDateTimeResponse_DateTimeRange `protobuf:"bytes,2,rep,name=pick_up_time_range,json=pickUpTimeRange,proto3" json:"pick_up_time_range,omitempty"`
}

func (x *GetAvailableDateTimeResponse) Reset() {
	*x = GetAvailableDateTimeResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDateTimeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDateTimeResponse) ProtoMessage() {}

func (x *GetAvailableDateTimeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDateTimeResponse.ProtoReflect.Descriptor instead.
func (*GetAvailableDateTimeResponse) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAvailableDateTimeResponse) GetArrivalTimeRange() []*GetAvailableDateTimeResponse_DateTimeRange {
	if x != nil {
		return x.ArrivalTimeRange
	}
	return nil
}

func (x *GetAvailableDateTimeResponse) GetPickUpTimeRange() []*GetAvailableDateTimeResponse_DateTimeRange {
	if x != nil {
		return x.PickUpTimeRange
	}
	return nil
}

// date time range
type GetAvailableDateTimeResponse_DateTimeRange struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// date
	Date *date.Date `protobuf:"bytes,1,opt,name=date,proto3" json:"date,omitempty"`
	// time range
	TimeRange []*v11.DayTimeRangeDef `protobuf:"bytes,2,rep,name=time_range,json=timeRange,proto3" json:"time_range,omitempty"`
}

func (x *GetAvailableDateTimeResponse_DateTimeRange) Reset() {
	*x = GetAvailableDateTimeResponse_DateTimeRange{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetAvailableDateTimeResponse_DateTimeRange) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAvailableDateTimeResponse_DateTimeRange) ProtoMessage() {}

func (x *GetAvailableDateTimeResponse_DateTimeRange) ProtoReflect() protoreflect.Message {
	mi := &file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAvailableDateTimeResponse_DateTimeRange.ProtoReflect.Descriptor instead.
func (*GetAvailableDateTimeResponse_DateTimeRange) Descriptor() ([]byte, []int) {
	return file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *GetAvailableDateTimeResponse_DateTimeRange) GetDate() *date.Date {
	if x != nil {
		return x.Date
	}
	return nil
}

func (x *GetAvailableDateTimeResponse_DateTimeRange) GetTimeRange() []*v11.DayTimeRangeDef {
	if x != nil {
		return x.TimeRange
	}
	return nil
}

var File_moego_service_online_booking_v1_ob_available_date_time_service_proto protoreflect.FileDescriptor

var file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDesc = []byte{
	0x0a, 0x44, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2f,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x74, 0x79, 0x70, 0x65, 0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x2b, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x66,
	0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x62, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73, 0x65, 0x74,
	0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe0, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x26, 0x0a, 0x0a, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42,
	0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x49, 0x64,
	0x12, 0x28, 0x0a, 0x0b, 0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x5f, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x20, 0x00, 0x52, 0x0a,
	0x62, 0x75, 0x73, 0x69, 0x6e, 0x65, 0x73, 0x73, 0x49, 0x64, 0x12, 0x2e, 0x0a, 0x09, 0x66, 0x72,
	0x6f, 0x6d, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65,
	0x52, 0x08, 0x66, 0x72, 0x6f, 0x6d, 0x44, 0x61, 0x74, 0x65, 0x12, 0x2a, 0x0a, 0x07, 0x74, 0x6f,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x06,
	0x74, 0x6f, 0x44, 0x61, 0x74, 0x65, 0x12, 0x61, 0x0a, 0x11, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x29, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x66, 0x66, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42,
	0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x49, 0x74, 0x65, 0x6d, 0x54, 0x79, 0x70, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x72, 0x65, 0x6c,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x03, 0x52, 0x12, 0x72, 0x65, 0x6c, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x22, 0x9c, 0x03, 0x0a, 0x1c,
	0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x79, 0x0a, 0x12,
	0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f,
	0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x52, 0x10, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69,
	0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x78, 0x0a, 0x12, 0x70, 0x69, 0x63, 0x6b, 0x5f,
	0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x4b, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x52, 0x0f, 0x70, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x1a, 0x86, 0x01, 0x0a, 0x0d, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61,
	0x6e, 0x67, 0x65, 0x12, 0x25, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x04, 0x64, 0x61, 0x74, 0x65, 0x12, 0x4e, 0x0a, 0x0a, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52,
	0x09, 0x74, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x32, 0xb2, 0x01, 0x0a, 0x1a, 0x4f,
	0x42, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x3c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c,
	0x65, 0x44, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x3d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x42,
	0x94, 0x01, 0x0a, 0x27, 0x63, 0x6f, 0x6d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64,
	0x6c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x50, 0x01, 0x5a, 0x67, 0x67,
	0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c,
	0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69,
	0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74,
	0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x73, 0x76, 0x63, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescOnce sync.Once
	file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescData = file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDesc
)

func file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescGZIP() []byte {
	file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescOnce.Do(func() {
		file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescData)
	})
	return file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDescData
}

var file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_moego_service_online_booking_v1_ob_available_date_time_service_proto_goTypes = []interface{}{
	(*GetAvailableDateTimeRequest)(nil),                // 0: moego.service.online_booking.v1.GetAvailableDateTimeRequest
	(*GetAvailableDateTimeResponse)(nil),               // 1: moego.service.online_booking.v1.GetAvailableDateTimeResponse
	(*GetAvailableDateTimeResponse_DateTimeRange)(nil), // 2: moego.service.online_booking.v1.GetAvailableDateTimeResponse.DateTimeRange
	(*date.Date)(nil),                                  // 3: google.type.Date
	(v1.ServiceItemType)(0),                            // 4: moego.models.offering.v1.ServiceItemType
	(*v11.DayTimeRangeDef)(nil),                        // 5: moego.models.online_booking.v1.DayTimeRangeDef
}
var file_moego_service_online_booking_v1_ob_available_date_time_service_proto_depIdxs = []int32{
	3, // 0: moego.service.online_booking.v1.GetAvailableDateTimeRequest.from_date:type_name -> google.type.Date
	3, // 1: moego.service.online_booking.v1.GetAvailableDateTimeRequest.to_date:type_name -> google.type.Date
	4, // 2: moego.service.online_booking.v1.GetAvailableDateTimeRequest.service_item_type:type_name -> moego.models.offering.v1.ServiceItemType
	2, // 3: moego.service.online_booking.v1.GetAvailableDateTimeResponse.arrival_time_range:type_name -> moego.service.online_booking.v1.GetAvailableDateTimeResponse.DateTimeRange
	2, // 4: moego.service.online_booking.v1.GetAvailableDateTimeResponse.pick_up_time_range:type_name -> moego.service.online_booking.v1.GetAvailableDateTimeResponse.DateTimeRange
	3, // 5: moego.service.online_booking.v1.GetAvailableDateTimeResponse.DateTimeRange.date:type_name -> google.type.Date
	5, // 6: moego.service.online_booking.v1.GetAvailableDateTimeResponse.DateTimeRange.time_range:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	0, // 7: moego.service.online_booking.v1.OBAvailableDateTimeService.GetAvailableDateTime:input_type -> moego.service.online_booking.v1.GetAvailableDateTimeRequest
	1, // 8: moego.service.online_booking.v1.OBAvailableDateTimeService.GetAvailableDateTime:output_type -> moego.service.online_booking.v1.GetAvailableDateTimeResponse
	8, // [8:9] is the sub-list for method output_type
	7, // [7:8] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_moego_service_online_booking_v1_ob_available_date_time_service_proto_init() }
func file_moego_service_online_booking_v1_ob_available_date_time_service_proto_init() {
	if File_moego_service_online_booking_v1_ob_available_date_time_service_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDateTimeRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDateTimeResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetAvailableDateTimeResponse_DateTimeRange); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_moego_service_online_booking_v1_ob_available_date_time_service_proto_goTypes,
		DependencyIndexes: file_moego_service_online_booking_v1_ob_available_date_time_service_proto_depIdxs,
		MessageInfos:      file_moego_service_online_booking_v1_ob_available_date_time_service_proto_msgTypes,
	}.Build()
	File_moego_service_online_booking_v1_ob_available_date_time_service_proto = out.File
	file_moego_service_online_booking_v1_ob_available_date_time_service_proto_rawDesc = nil
	file_moego_service_online_booking_v1_ob_available_date_time_service_proto_goTypes = nil
	file_moego_service_online_booking_v1_ob_available_date_time_service_proto_depIdxs = nil
}
