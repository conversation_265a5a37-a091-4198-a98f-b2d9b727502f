// @since 2024-04-08 15:28:27
// <AUTHOR> <zhang<PERSON>@moego.pet>

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        (unknown)
// source: moego/models/online_booking/v1/ob_availability_setting_defs.proto

package onlinebookingpb

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/customer/v1"
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	date "google.golang.org/genproto/googleapis/type/date"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// grooming service availability update def
type GroomingServiceAvailabilityUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accept customer type
	AcceptCustomerType *AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType,oneof" json:"accept_customer_type,omitempty"`
}

func (x *GroomingServiceAvailabilityUpdateDef) Reset() {
	*x = GroomingServiceAvailabilityUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GroomingServiceAvailabilityUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroomingServiceAvailabilityUpdateDef) ProtoMessage() {}

func (x *GroomingServiceAvailabilityUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroomingServiceAvailabilityUpdateDef.ProtoReflect.Descriptor instead.
func (*GroomingServiceAvailabilityUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{0}
}

func (x *GroomingServiceAvailabilityUpdateDef) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil && x.AcceptCustomerType != nil {
		return *x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

// boarding service availability update def
type BoardingServiceAvailabilityUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted pet types
	AcceptedPetTypes []v1.PetType `protobuf:"varint,1,rep,packed,name=accepted_pet_types,json=acceptedPetTypes,proto3,enum=moego.models.customer.v1.PetType" json:"accepted_pet_types,omitempty"`
	// available date range
	AvailableDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=available_date_range,json=availableDateRange,proto3" json:"available_date_range,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *ArrivalPickUpTimeDef `protobuf:"bytes,3,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3,oneof" json:"arrival_pick_up_time_range,omitempty"`
	// accept customer type
	AcceptCustomerType *AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType,oneof" json:"accept_customer_type,omitempty"`
	// lodging availability
	LodgingAvailability *LodgingAvailabilityDef `protobuf:"bytes,5,opt,name=lodging_availability,json=lodgingAvailability,proto3,oneof" json:"lodging_availability,omitempty"`
}

func (x *BoardingServiceAvailabilityUpdateDef) Reset() {
	*x = BoardingServiceAvailabilityUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BoardingServiceAvailabilityUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoardingServiceAvailabilityUpdateDef) ProtoMessage() {}

func (x *BoardingServiceAvailabilityUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoardingServiceAvailabilityUpdateDef.ProtoReflect.Descriptor instead.
func (*BoardingServiceAvailabilityUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{1}
}

func (x *BoardingServiceAvailabilityUpdateDef) GetAcceptedPetTypes() []v1.PetType {
	if x != nil {
		return x.AcceptedPetTypes
	}
	return nil
}

func (x *BoardingServiceAvailabilityUpdateDef) GetAvailableDateRange() *DateRangeDef {
	if x != nil {
		return x.AvailableDateRange
	}
	return nil
}

func (x *BoardingServiceAvailabilityUpdateDef) GetArrivalPickUpTimeRange() *ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

func (x *BoardingServiceAvailabilityUpdateDef) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil && x.AcceptCustomerType != nil {
		return *x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *BoardingServiceAvailabilityUpdateDef) GetLodgingAvailability() *LodgingAvailabilityDef {
	if x != nil {
		return x.LodgingAvailability
	}
	return nil
}

// daycare service availability update def
type DaycareServiceAvailabilityUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// accepted pet types
	AcceptedPetTypes []v1.PetType `protobuf:"varint,1,rep,packed,name=accepted_pet_types,json=acceptedPetTypes,proto3,enum=moego.models.customer.v1.PetType" json:"accepted_pet_types,omitempty"`
	// available date range
	AvailableDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=available_date_range,json=availableDateRange,proto3" json:"available_date_range,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *ArrivalPickUpTimeDef `protobuf:"bytes,3,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3,oneof" json:"arrival_pick_up_time_range,omitempty"`
	// accept customer type
	AcceptCustomerType *AcceptCustomerType `protobuf:"varint,4,opt,name=accept_customer_type,json=acceptCustomerType,proto3,enum=moego.models.online_booking.v1.AcceptCustomerType,oneof" json:"accept_customer_type,omitempty"`
	// lodging availability
	LodgingAvailability *LodgingAvailabilityDef `protobuf:"bytes,5,opt,name=lodging_availability,json=lodgingAvailability,proto3,oneof" json:"lodging_availability,omitempty"`
}

func (x *DaycareServiceAvailabilityUpdateDef) Reset() {
	*x = DaycareServiceAvailabilityUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DaycareServiceAvailabilityUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DaycareServiceAvailabilityUpdateDef) ProtoMessage() {}

func (x *DaycareServiceAvailabilityUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DaycareServiceAvailabilityUpdateDef.ProtoReflect.Descriptor instead.
func (*DaycareServiceAvailabilityUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{2}
}

func (x *DaycareServiceAvailabilityUpdateDef) GetAcceptedPetTypes() []v1.PetType {
	if x != nil {
		return x.AcceptedPetTypes
	}
	return nil
}

func (x *DaycareServiceAvailabilityUpdateDef) GetAvailableDateRange() *DateRangeDef {
	if x != nil {
		return x.AvailableDateRange
	}
	return nil
}

func (x *DaycareServiceAvailabilityUpdateDef) GetArrivalPickUpTimeRange() *ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

func (x *DaycareServiceAvailabilityUpdateDef) GetAcceptCustomerType() AcceptCustomerType {
	if x != nil && x.AcceptCustomerType != nil {
		return *x.AcceptCustomerType
	}
	return AcceptCustomerType_ACCEPT_CUSTOMER_TYPE_UNSPECIFIED
}

func (x *DaycareServiceAvailabilityUpdateDef) GetLodgingAvailability() *LodgingAvailabilityDef {
	if x != nil {
		return x.LodgingAvailability
	}
	return nil
}

// evaluation service availability update def
type EvaluationServiceAvailabilityUpdateDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// available date range
	AvailableDateRange *DateRangeDef `protobuf:"bytes,2,opt,name=available_date_range,json=availableDateRange,proto3" json:"available_date_range,omitempty"`
	// pick up time range
	ArrivalPickUpTimeRange *ArrivalPickUpTimeDef `protobuf:"bytes,3,opt,name=arrival_pick_up_time_range,json=arrivalPickUpTimeRange,proto3" json:"arrival_pick_up_time_range,omitempty"`
}

func (x *EvaluationServiceAvailabilityUpdateDef) Reset() {
	*x = EvaluationServiceAvailabilityUpdateDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EvaluationServiceAvailabilityUpdateDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EvaluationServiceAvailabilityUpdateDef) ProtoMessage() {}

func (x *EvaluationServiceAvailabilityUpdateDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EvaluationServiceAvailabilityUpdateDef.ProtoReflect.Descriptor instead.
func (*EvaluationServiceAvailabilityUpdateDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{3}
}

func (x *EvaluationServiceAvailabilityUpdateDef) GetAvailableDateRange() *DateRangeDef {
	if x != nil {
		return x.AvailableDateRange
	}
	return nil
}

func (x *EvaluationServiceAvailabilityUpdateDef) GetArrivalPickUpTimeRange() *ArrivalPickUpTimeDef {
	if x != nil {
		return x.ArrivalPickUpTimeRange
	}
	return nil
}

// booking range model
type DateRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date type
	StartDateType DateLimitType `protobuf:"varint,1,opt,name=start_date_type,json=startDateType,proto3,enum=moego.models.online_booking.v1.DateLimitType" json:"start_date_type,omitempty"`
	// specific date or max date offset from today
	//
	// Types that are assignable to StartDate:
	//
	//	*DateRangeDef_SpecificStartDate
	//	*DateRangeDef_MaxStartDateOffset
	StartDate isDateRangeDef_StartDate `protobuf_oneof:"start_date"`
	// start date type
	EndDateType DateLimitType `protobuf:"varint,4,opt,name=end_date_type,json=endDateType,proto3,enum=moego.models.online_booking.v1.DateLimitType" json:"end_date_type,omitempty"`
	// specific date or max date offset from today
	//
	// Types that are assignable to EndDate:
	//
	//	*DateRangeDef_SpecificEndDate
	//	*DateRangeDef_MaxEndDateOffset
	EndDate isDateRangeDef_EndDate `protobuf_oneof:"end_date"`
}

func (x *DateRangeDef) Reset() {
	*x = DateRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DateRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DateRangeDef) ProtoMessage() {}

func (x *DateRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DateRangeDef.ProtoReflect.Descriptor instead.
func (*DateRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{4}
}

func (x *DateRangeDef) GetStartDateType() DateLimitType {
	if x != nil {
		return x.StartDateType
	}
	return DateLimitType_DATE_TYPE_UNSPECIFIED
}

func (m *DateRangeDef) GetStartDate() isDateRangeDef_StartDate {
	if m != nil {
		return m.StartDate
	}
	return nil
}

func (x *DateRangeDef) GetSpecificStartDate() *date.Date {
	if x, ok := x.GetStartDate().(*DateRangeDef_SpecificStartDate); ok {
		return x.SpecificStartDate
	}
	return nil
}

func (x *DateRangeDef) GetMaxStartDateOffset() int32 {
	if x, ok := x.GetStartDate().(*DateRangeDef_MaxStartDateOffset); ok {
		return x.MaxStartDateOffset
	}
	return 0
}

func (x *DateRangeDef) GetEndDateType() DateLimitType {
	if x != nil {
		return x.EndDateType
	}
	return DateLimitType_DATE_TYPE_UNSPECIFIED
}

func (m *DateRangeDef) GetEndDate() isDateRangeDef_EndDate {
	if m != nil {
		return m.EndDate
	}
	return nil
}

func (x *DateRangeDef) GetSpecificEndDate() *date.Date {
	if x, ok := x.GetEndDate().(*DateRangeDef_SpecificEndDate); ok {
		return x.SpecificEndDate
	}
	return nil
}

func (x *DateRangeDef) GetMaxEndDateOffset() int32 {
	if x, ok := x.GetEndDate().(*DateRangeDef_MaxEndDateOffset); ok {
		return x.MaxEndDateOffset
	}
	return 0
}

type isDateRangeDef_StartDate interface {
	isDateRangeDef_StartDate()
}

type DateRangeDef_SpecificStartDate struct {
	// specific start date
	SpecificStartDate *date.Date `protobuf:"bytes,2,opt,name=specific_start_date,json=specificStartDate,proto3,oneof"`
}

type DateRangeDef_MaxStartDateOffset struct {
	// dynamic setting, max start date offset from today
	MaxStartDateOffset int32 `protobuf:"varint,3,opt,name=max_start_date_offset,json=maxStartDateOffset,proto3,oneof"`
}

func (*DateRangeDef_SpecificStartDate) isDateRangeDef_StartDate() {}

func (*DateRangeDef_MaxStartDateOffset) isDateRangeDef_StartDate() {}

type isDateRangeDef_EndDate interface {
	isDateRangeDef_EndDate()
}

type DateRangeDef_SpecificEndDate struct {
	// specific end date
	SpecificEndDate *date.Date `protobuf:"bytes,5,opt,name=specific_end_date,json=specificEndDate,proto3,oneof"`
}

type DateRangeDef_MaxEndDateOffset struct {
	// dynamic setting, max end date offset from today
	MaxEndDateOffset int32 `protobuf:"varint,6,opt,name=max_end_date_offset,json=maxEndDateOffset,proto3,oneof"`
}

func (*DateRangeDef_SpecificEndDate) isDateRangeDef_EndDate() {}

func (*DateRangeDef_MaxEndDateOffset) isDateRangeDef_EndDate() {}

// arrival and pick up time
type ArrivalPickUpTimeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// is customized
	IsCustomized bool `protobuf:"varint,1,opt,name=is_customized,json=isCustomized,proto3" json:"is_customized,omitempty"`
	// start date
	StartDate *date.Date `protobuf:"bytes,2,opt,name=start_date,json=startDate,proto3,oneof" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,3,opt,name=end_date,json=endDate,proto3,oneof" json:"end_date,omitempty"`
	// schedule type
	ScheduleType ScheduleType `protobuf:"varint,4,opt,name=schedule_type,json=scheduleType,proto3,enum=moego.models.online_booking.v1.ScheduleType" json:"schedule_type,omitempty"`
	// service ids
	ServiceIds []int64 `protobuf:"varint,5,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	// is all services
	IsAllService bool `protobuf:"varint,6,opt,name=is_all_service,json=isAllService,proto3" json:"is_all_service,omitempty"`
	// arrival time range
	ArrivalTimeRange *TimeRangeDef `protobuf:"bytes,9,opt,name=arrival_time_range,json=arrivalTimeRange,proto3" json:"arrival_time_range,omitempty"`
	// pick up time range
	PickUpTimeRange *TimeRangeDef `protobuf:"bytes,10,opt,name=pick_up_time_range,json=pickUpTimeRange,proto3" json:"pick_up_time_range,omitempty"`
	// time range setting id
	TimeRangeSettingId *int64 `protobuf:"varint,11,opt,name=time_range_setting_id,json=timeRangeSettingId,proto3,oneof" json:"time_range_setting_id,omitempty"`
}

func (x *ArrivalPickUpTimeDef) Reset() {
	*x = ArrivalPickUpTimeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ArrivalPickUpTimeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArrivalPickUpTimeDef) ProtoMessage() {}

func (x *ArrivalPickUpTimeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArrivalPickUpTimeDef.ProtoReflect.Descriptor instead.
func (*ArrivalPickUpTimeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{5}
}

func (x *ArrivalPickUpTimeDef) GetIsCustomized() bool {
	if x != nil {
		return x.IsCustomized
	}
	return false
}

func (x *ArrivalPickUpTimeDef) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *ArrivalPickUpTimeDef) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

func (x *ArrivalPickUpTimeDef) GetScheduleType() ScheduleType {
	if x != nil {
		return x.ScheduleType
	}
	return ScheduleType_SCHEDULE_TYPE_UNSPECIFIED
}

func (x *ArrivalPickUpTimeDef) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

func (x *ArrivalPickUpTimeDef) GetIsAllService() bool {
	if x != nil {
		return x.IsAllService
	}
	return false
}

func (x *ArrivalPickUpTimeDef) GetArrivalTimeRange() *TimeRangeDef {
	if x != nil {
		return x.ArrivalTimeRange
	}
	return nil
}

func (x *ArrivalPickUpTimeDef) GetPickUpTimeRange() *TimeRangeDef {
	if x != nil {
		return x.PickUpTimeRange
	}
	return nil
}

func (x *ArrivalPickUpTimeDef) GetTimeRangeSettingId() int64 {
	if x != nil && x.TimeRangeSettingId != nil {
		return *x.TimeRangeSettingId
	}
	return 0
}

// time range model
type TimeRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// first week
	FirstWeek *DayOfWeekTimeRangeDef `protobuf:"bytes,4,opt,name=first_week,json=firstWeek,proto3" json:"first_week,omitempty"`
	// second week
	SecondWeek *DayOfWeekTimeRangeDef `protobuf:"bytes,5,opt,name=second_week,json=secondWeek,proto3,oneof" json:"second_week,omitempty"`
	// third week
	ThirdWeek *DayOfWeekTimeRangeDef `protobuf:"bytes,6,opt,name=third_week,json=thirdWeek,proto3,oneof" json:"third_week,omitempty"`
	// forth week
	ForthWeek *DayOfWeekTimeRangeDef `protobuf:"bytes,7,opt,name=forth_week,json=forthWeek,proto3,oneof" json:"forth_week,omitempty"`
}

func (x *TimeRangeDef) Reset() {
	*x = TimeRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TimeRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TimeRangeDef) ProtoMessage() {}

func (x *TimeRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TimeRangeDef.ProtoReflect.Descriptor instead.
func (*TimeRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{6}
}

func (x *TimeRangeDef) GetFirstWeek() *DayOfWeekTimeRangeDef {
	if x != nil {
		return x.FirstWeek
	}
	return nil
}

func (x *TimeRangeDef) GetSecondWeek() *DayOfWeekTimeRangeDef {
	if x != nil {
		return x.SecondWeek
	}
	return nil
}

func (x *TimeRangeDef) GetThirdWeek() *DayOfWeekTimeRangeDef {
	if x != nil {
		return x.ThirdWeek
	}
	return nil
}

func (x *TimeRangeDef) GetForthWeek() *DayOfWeekTimeRangeDef {
	if x != nil {
		return x.ForthWeek
	}
	return nil
}

// time detail
type DayOfWeekTimeRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// monday
	Monday []*DayTimeRangeDef `protobuf:"bytes,1,rep,name=monday,proto3" json:"monday,omitempty"`
	// tuesday
	Tuesday []*DayTimeRangeDef `protobuf:"bytes,2,rep,name=tuesday,proto3" json:"tuesday,omitempty"`
	// wednesday
	Wednesday []*DayTimeRangeDef `protobuf:"bytes,3,rep,name=wednesday,proto3" json:"wednesday,omitempty"`
	// thursday
	Thursday []*DayTimeRangeDef `protobuf:"bytes,4,rep,name=thursday,proto3" json:"thursday,omitempty"`
	// friday
	Friday []*DayTimeRangeDef `protobuf:"bytes,5,rep,name=friday,proto3" json:"friday,omitempty"`
	// saturday
	Saturday []*DayTimeRangeDef `protobuf:"bytes,6,rep,name=saturday,proto3" json:"saturday,omitempty"`
	// sunday
	Sunday []*DayTimeRangeDef `protobuf:"bytes,7,rep,name=sunday,proto3" json:"sunday,omitempty"`
}

func (x *DayOfWeekTimeRangeDef) Reset() {
	*x = DayOfWeekTimeRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DayOfWeekTimeRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DayOfWeekTimeRangeDef) ProtoMessage() {}

func (x *DayOfWeekTimeRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DayOfWeekTimeRangeDef.ProtoReflect.Descriptor instead.
func (*DayOfWeekTimeRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{7}
}

func (x *DayOfWeekTimeRangeDef) GetMonday() []*DayTimeRangeDef {
	if x != nil {
		return x.Monday
	}
	return nil
}

func (x *DayOfWeekTimeRangeDef) GetTuesday() []*DayTimeRangeDef {
	if x != nil {
		return x.Tuesday
	}
	return nil
}

func (x *DayOfWeekTimeRangeDef) GetWednesday() []*DayTimeRangeDef {
	if x != nil {
		return x.Wednesday
	}
	return nil
}

func (x *DayOfWeekTimeRangeDef) GetThursday() []*DayTimeRangeDef {
	if x != nil {
		return x.Thursday
	}
	return nil
}

func (x *DayOfWeekTimeRangeDef) GetFriday() []*DayTimeRangeDef {
	if x != nil {
		return x.Friday
	}
	return nil
}

func (x *DayOfWeekTimeRangeDef) GetSaturday() []*DayTimeRangeDef {
	if x != nil {
		return x.Saturday
	}
	return nil
}

func (x *DayOfWeekTimeRangeDef) GetSunday() []*DayTimeRangeDef {
	if x != nil {
		return x.Sunday
	}
	return nil
}

// time range
type DayTimeRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start time
	StartTime int32 `protobuf:"varint,1,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	// end time
	EndTime int32 `protobuf:"varint,2,opt,name=end_time,json=endTime,proto3" json:"end_time,omitempty"`
	// max pet count can take during this time range. Available for evaluation arrival time range
	PetCapacity *int32 `protobuf:"varint,3,opt,name=pet_capacity,json=petCapacity,proto3,oneof" json:"pet_capacity,omitempty"`
}

func (x *DayTimeRangeDef) Reset() {
	*x = DayTimeRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DayTimeRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DayTimeRangeDef) ProtoMessage() {}

func (x *DayTimeRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DayTimeRangeDef.ProtoReflect.Descriptor instead.
func (*DayTimeRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{8}
}

func (x *DayTimeRangeDef) GetStartTime() int32 {
	if x != nil {
		return x.StartTime
	}
	return 0
}

func (x *DayTimeRangeDef) GetEndTime() int32 {
	if x != nil {
		return x.EndTime
	}
	return 0
}

func (x *DayTimeRangeDef) GetPetCapacity() int32 {
	if x != nil && x.PetCapacity != nil {
		return *x.PetCapacity
	}
	return 0
}

// lodging availability
type LodgingAvailabilityDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// if limit requests based on  lodging/area capacity
	IsCapacityLimited bool `protobuf:"varint,1,opt,name=is_capacity_limited,json=isCapacityLimited,proto3" json:"is_capacity_limited,omitempty"`
	// limit requests based on service related lodging/area capacity
	CapacityLimit int32 `protobuf:"varint,2,opt,name=capacity_limit,json=capacityLimit,proto3" json:"capacity_limit,omitempty"`
	// allow waitlist signups
	AllowWaitlistSignups *bool `protobuf:"varint,3,opt,name=allow_waitlist_signups,json=allowWaitlistSignups,proto3,oneof" json:"allow_waitlist_signups,omitempty"`
}

func (x *LodgingAvailabilityDef) Reset() {
	*x = LodgingAvailabilityDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LodgingAvailabilityDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingAvailabilityDef) ProtoMessage() {}

func (x *LodgingAvailabilityDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingAvailabilityDef.ProtoReflect.Descriptor instead.
func (*LodgingAvailabilityDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{9}
}

func (x *LodgingAvailabilityDef) GetIsCapacityLimited() bool {
	if x != nil {
		return x.IsCapacityLimited
	}
	return false
}

func (x *LodgingAvailabilityDef) GetCapacityLimit() int32 {
	if x != nil {
		return x.CapacityLimit
	}
	return 0
}

func (x *LodgingAvailabilityDef) GetAllowWaitlistSignups() bool {
	if x != nil && x.AllowWaitlistSignups != nil {
		return *x.AllowWaitlistSignups
	}
	return false
}

// day time range list
type DayTimeRangeDefList struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The list of values
	Values []*DayTimeRangeDef `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty"`
}

func (x *DayTimeRangeDefList) Reset() {
	*x = DayTimeRangeDefList{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DayTimeRangeDefList) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DayTimeRangeDefList) ProtoMessage() {}

func (x *DayTimeRangeDefList) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DayTimeRangeDefList.ProtoReflect.Descriptor instead.
func (*DayTimeRangeDefList) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{10}
}

func (x *DayTimeRangeDefList) GetValues() []*DayTimeRangeDef {
	if x != nil {
		return x.Values
	}
	return nil
}

// capacity override model
type CapacityOverrideDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// id, empty for create new record
	Id *int64 `protobuf:"varint,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// date ranges
	DateRanges []*CapacityOverrideDef_CapacityDateRangeDef `protobuf:"bytes,2,rep,name=date_ranges,json=dateRanges,proto3" json:"date_ranges,omitempty"`
	// capacity
	Capacity int32 `protobuf:"varint,3,opt,name=capacity,proto3" json:"capacity,omitempty"`
	// unit type
	UnitType CapacityOverrideUnitType `protobuf:"varint,4,opt,name=unit_type,json=unitType,proto3,enum=moego.models.online_booking.v1.CapacityOverrideUnitType" json:"unit_type,omitempty"`
}

func (x *CapacityOverrideDef) Reset() {
	*x = CapacityOverrideDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapacityOverrideDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapacityOverrideDef) ProtoMessage() {}

func (x *CapacityOverrideDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapacityOverrideDef.ProtoReflect.Descriptor instead.
func (*CapacityOverrideDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{11}
}

func (x *CapacityOverrideDef) GetId() int64 {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return 0
}

func (x *CapacityOverrideDef) GetDateRanges() []*CapacityOverrideDef_CapacityDateRangeDef {
	if x != nil {
		return x.DateRanges
	}
	return nil
}

func (x *CapacityOverrideDef) GetCapacity() int32 {
	if x != nil {
		return x.Capacity
	}
	return 0
}

func (x *CapacityOverrideDef) GetUnitType() CapacityOverrideUnitType {
	if x != nil {
		return x.UnitType
	}
	return CapacityOverrideUnitType_CAPACITY_OVERRIDE_UNIT_TYPE_UNSPECIFIED
}

// capacity date range def
type CapacityOverrideDef_CapacityDateRangeDef struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// start date
	StartDate *date.Date `protobuf:"bytes,1,opt,name=start_date,json=startDate,proto3" json:"start_date,omitempty"`
	// end date
	EndDate *date.Date `protobuf:"bytes,2,opt,name=end_date,json=endDate,proto3" json:"end_date,omitempty"`
}

func (x *CapacityOverrideDef_CapacityDateRangeDef) Reset() {
	*x = CapacityOverrideDef_CapacityDateRangeDef{}
	if protoimpl.UnsafeEnabled {
		mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapacityOverrideDef_CapacityDateRangeDef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapacityOverrideDef_CapacityDateRangeDef) ProtoMessage() {}

func (x *CapacityOverrideDef_CapacityDateRangeDef) ProtoReflect() protoreflect.Message {
	mi := &file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapacityOverrideDef_CapacityDateRangeDef.ProtoReflect.Descriptor instead.
func (*CapacityOverrideDef_CapacityDateRangeDef) Descriptor() ([]byte, []int) {
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP(), []int{11, 0}
}

func (x *CapacityOverrideDef_CapacityDateRangeDef) GetStartDate() *date.Date {
	if x != nil {
		return x.StartDate
	}
	return nil
}

func (x *CapacityOverrideDef_CapacityDateRangeDef) GetEndDate() *date.Date {
	if x != nil {
		return x.EndDate
	}
	return nil
}

var File_moego_models_online_booking_v1_ob_availability_setting_defs_proto protoreflect.FileDescriptor

var file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDesc = []byte{
	0x0a, 0x41, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31,
	0x2f, 0x6f, 0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79,
	0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x65, 0x66, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x1a, 0x16, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x74, 0x79, 0x70, 0x65,
	0x2f, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x31, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x70,
	0x65, 0x74, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x42,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x62, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x5f, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x6e, 0x75, 0x6d, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb6, 0x01, 0x0a, 0x24,
	0x47, 0x72, 0x6f, 0x6f, 0x6d, 0x69, 0x6e, 0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x44, 0x65, 0x66, 0x12, 0x75, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01,
	0x20, 0x00, 0x48, 0x00, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x42, 0x17, 0x0a, 0x15, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x22, 0x86, 0x05, 0x0a, 0x24, 0x42, 0x6f, 0x61, 0x72, 0x64, 0x69, 0x6e,
	0x67, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x4f, 0x0a,
	0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67,
	0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x63,
	0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5e,
	0x0a, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x12, 0x61, 0x76, 0x61, 0x69,
	0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x75,
	0x0a, 0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75,
	0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x75, 0x0a, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f,
	0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10,
	0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x6e, 0x0a, 0x14,
	0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69,
	0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67,
	0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44,
	0x65, 0x66, 0x48, 0x02, 0x52, 0x13, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b,
	0x5f, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f,
	0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67,
	0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0x85, 0x05,
	0x0a, 0x23, 0x44, 0x61, 0x79, 0x63, 0x61, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x65, 0x66, 0x12, 0x4f, 0x0a, 0x12, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65,
	0x64, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28,
	0x0e, 0x32, 0x21, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x65, 0x74,
	0x54, 0x79, 0x70, 0x65, 0x52, 0x10, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x65, 0x64, 0x50, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x73, 0x12, 0x5e, 0x0a, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61,
	0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44,
	0x65, 0x66, 0x52, 0x12, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x75, 0x0a, 0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66,
	0x48, 0x00, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x88, 0x01, 0x01, 0x12, 0x75, 0x0a,
	0x14, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x42,
	0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x48, 0x01, 0x52, 0x12, 0x61,
	0x63, 0x63, 0x65, 0x70, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x6e, 0x0a, 0x14, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f,
	0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x36, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x13, 0x6c, 0x6f,
	0x64, 0x67, 0x69, 0x6e, 0x67, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74,
	0x79, 0x88, 0x01, 0x01, 0x42, 0x1d, 0x0a, 0x1b, 0x5f, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c,
	0x5f, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61,
	0x6e, 0x67, 0x65, 0x42, 0x17, 0x0a, 0x15, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x5f, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x42, 0x17, 0x0a, 0x15,
	0x5f, 0x6c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62,
	0x69, 0x6c, 0x69, 0x74, 0x79, 0x22, 0xfa, 0x01, 0x0a, 0x26, 0x45, 0x76, 0x61, 0x6c, 0x75, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x76, 0x61, 0x69, 0x6c,
	0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x65, 0x66,
	0x12, 0x5e, 0x0a, 0x14, 0x61, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x12, 0x61, 0x76,
	0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x70, 0x0a, 0x1a, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x70, 0x69, 0x63, 0x6b,
	0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x50, 0x69, 0x63,
	0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x52, 0x16, 0x61, 0x72, 0x72, 0x69,
	0x76, 0x61, 0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x22, 0xe0, 0x03, 0x0a, 0x0c, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x44, 0x65, 0x66, 0x12, 0x55, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0d, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x43, 0x0a, 0x13, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x11, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x12,
	0x3f, 0x0a, 0x15, 0x6d, 0x61, 0x78, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a,
	0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xed, 0x02, 0x28, 0x00, 0x48, 0x00, 0x52, 0x12, 0x6d, 0x61,
	0x78, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x73, 0x65, 0x74,
	0x12, 0x51, 0x0a, 0x0d, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x3f, 0x0a, 0x11, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74,
	0x65, 0x48, 0x01, 0x52, 0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x45, 0x6e, 0x64,
	0x44, 0x61, 0x74, 0x65, 0x12, 0x3b, 0x0a, 0x13, 0x6d, 0x61, 0x78, 0x5f, 0x65, 0x6e, 0x64, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x6f, 0x66, 0x66, 0x73, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x18, 0xed, 0x02, 0x28, 0x00, 0x48, 0x01, 0x52,
	0x10, 0x6d, 0x61, 0x78, 0x45, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x66, 0x66, 0x73, 0x65,
	0x74, 0x42, 0x11, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x03, 0xf8, 0x42, 0x01, 0x42, 0x0f, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x03, 0xf8, 0x42, 0x01, 0x22, 0xf0, 0x04, 0x0a, 0x14, 0x41, 0x72, 0x72, 0x69, 0x76, 0x61,
	0x6c, 0x50, 0x69, 0x63, 0x6b, 0x55, 0x70, 0x54, 0x69, 0x6d, 0x65, 0x44, 0x65, 0x66, 0x12, 0x23,
	0x0a, 0x0d, 0x69, 0x73, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69, 0x7a, 0x65, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x69,
	0x7a, 0x65, 0x64, 0x12, 0x35, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48, 0x00, 0x52, 0x09, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x31, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70, 0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x48,
	0x01, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12, 0x5d, 0x0a,
	0x0d, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x0c,
	0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28,
	0x03, 0x52, 0x0a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x49, 0x64, 0x73, 0x12, 0x24, 0x0a,
	0x0e, 0x69, 0x73, 0x5f, 0x61, 0x6c, 0x6c, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x69, 0x73, 0x41, 0x6c, 0x6c, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x12, 0x5a, 0x0a, 0x12, 0x61, 0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x10, 0x61,
	0x72, 0x72, 0x69, 0x76, 0x61, 0x6c, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x59, 0x0a, 0x12, 0x70, 0x69, 0x63, 0x6b, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f,
	0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x0f, 0x70, 0x69, 0x63, 0x6b, 0x55,
	0x70, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x36, 0x0a, 0x15, 0x74, 0x69,
	0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67,
	0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x03, 0x48, 0x02, 0x52, 0x12, 0x74, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x88,
	0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x42, 0x18,
	0x0a, 0x16, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x64, 0x22, 0xaf, 0x03, 0x0a, 0x0c, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x5e, 0x0a, 0x0a, 0x66, 0x69, 0x72,
	0x73, 0x74, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e,
	0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c,
	0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x8a, 0x01, 0x02, 0x10, 0x01, 0x52, 0x09,
	0x66, 0x69, 0x72, 0x73, 0x74, 0x57, 0x65, 0x65, 0x6b, 0x12, 0x5b, 0x0a, 0x0b, 0x73, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35,
	0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e,
	0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x44, 0x65, 0x66, 0x48, 0x00, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x57,
	0x65, 0x65, 0x6b, 0x88, 0x01, 0x01, 0x12, 0x59, 0x0a, 0x0a, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f,
	0x77, 0x65, 0x65, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65,
	0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x4f,
	0x66, 0x57, 0x65, 0x65, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65,
	0x66, 0x48, 0x01, 0x52, 0x09, 0x74, 0x68, 0x69, 0x72, 0x64, 0x57, 0x65, 0x65, 0x6b, 0x88, 0x01,
	0x01, 0x12, 0x59, 0x0a, 0x0a, 0x66, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b,
	0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x48, 0x02, 0x52, 0x09,
	0x66, 0x6f, 0x72, 0x74, 0x68, 0x57, 0x65, 0x65, 0x6b, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c,
	0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x42, 0x0d, 0x0a, 0x0b,
	0x5f, 0x74, 0x68, 0x69, 0x72, 0x64, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x42, 0x0d, 0x0a, 0x0b, 0x5f,
	0x66, 0x6f, 0x72, 0x74, 0x68, 0x5f, 0x77, 0x65, 0x65, 0x6b, 0x22, 0xec, 0x04, 0x0a, 0x15, 0x44,
	0x61, 0x79, 0x4f, 0x66, 0x57, 0x65, 0x65, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67,
	0x65, 0x44, 0x65, 0x66, 0x12, 0x51, 0x0a, 0x06, 0x6d, 0x6f, 0x6e, 0x64, 0x61, 0x79, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e,
	0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52,
	0x06, 0x6d, 0x6f, 0x6e, 0x64, 0x61, 0x79, 0x12, 0x53, 0x0a, 0x07, 0x74, 0x75, 0x65, 0x73, 0x64,
	0x61, 0x79, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f,
	0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x28, 0x01, 0x52, 0x07, 0x74, 0x75, 0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x57, 0x0a, 0x09,
	0x77, 0x65, 0x64, 0x6e, 0x65, 0x73, 0x64, 0x61, 0x79, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66,
	0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x09, 0x77, 0x65, 0x64, 0x6e,
	0x65, 0x73, 0x64, 0x61, 0x79, 0x12, 0x55, 0x0a, 0x08, 0x74, 0x68, 0x75, 0x72, 0x73, 0x64, 0x61,
	0x79, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65,
	0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02,
	0x28, 0x01, 0x52, 0x08, 0x74, 0x68, 0x75, 0x72, 0x73, 0x64, 0x61, 0x79, 0x12, 0x51, 0x0a, 0x06,
	0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69,
	0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa,
	0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x06, 0x66, 0x72, 0x69, 0x64, 0x61, 0x79, 0x12,
	0x55, 0x0a, 0x08, 0x73, 0x61, 0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x18, 0x06, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73,
	0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44,
	0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28, 0x01, 0x52, 0x08, 0x73, 0x61,
	0x74, 0x75, 0x72, 0x64, 0x61, 0x79, 0x12, 0x51, 0x0a, 0x06, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01, 0x02, 0x28,
	0x01, 0x52, 0x06, 0x73, 0x75, 0x6e, 0x64, 0x61, 0x79, 0x22, 0xa7, 0x01, 0x0a, 0x0f, 0x44, 0x61,
	0x79, 0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12, 0x29, 0x0a,
	0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a, 0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x09, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x1a,
	0x05, 0x10, 0xa0, 0x0b, 0x28, 0x00, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x31, 0x0a, 0x0c, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x64, 0x28, 0x00,
	0x48, 0x00, 0x52, 0x0b, 0x70, 0x65, 0x74, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x88,
	0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x70, 0x65, 0x74, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x22, 0xce, 0x01, 0x0a, 0x16, 0x4c, 0x6f, 0x64, 0x67, 0x69, 0x6e, 0x67, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x44, 0x65, 0x66, 0x12, 0x2e,
	0x0a, 0x13, 0x69, 0x73, 0x5f, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x43,
	0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x65, 0x64, 0x12, 0x2e,
	0x0a, 0x0e, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52,
	0x0d, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x39,
	0x0a, 0x16, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74,
	0x5f, 0x73, 0x69, 0x67, 0x6e, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x48, 0x00,
	0x52, 0x14, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x57, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x53,
	0x69, 0x67, 0x6e, 0x75, 0x70, 0x73, 0x88, 0x01, 0x01, 0x42, 0x19, 0x0a, 0x17, 0x5f, 0x61, 0x6c,
	0x6c, 0x6f, 0x77, 0x5f, 0x77, 0x61, 0x69, 0x74, 0x6c, 0x69, 0x73, 0x74, 0x5f, 0x73, 0x69, 0x67,
	0x6e, 0x75, 0x70, 0x73, 0x22, 0x5e, 0x0a, 0x13, 0x44, 0x61, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x52,
	0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x6d, 0x6f,
	0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e,
	0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x61, 0x79,
	0x54, 0x69, 0x6d, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x52, 0x06, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x73, 0x22, 0xaf, 0x03, 0x0a, 0x13, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x44, 0x65, 0x66, 0x12, 0x1c, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x22, 0x02, 0x28,
	0x00, 0x48, 0x00, 0x52, 0x02, 0x69, 0x64, 0x88, 0x01, 0x01, 0x12, 0x73, 0x0a, 0x0b, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x48, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f,
	0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x44, 0x65, 0x66, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x44, 0x61, 0x74,
	0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x92, 0x01,
	0x02, 0x08, 0x01, 0x52, 0x0a, 0x64, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x73, 0x12,
	0x23, 0x0a, 0x08, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x1a, 0x02, 0x28, 0x00, 0x52, 0x08, 0x63, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x12, 0x61, 0x0a, 0x09, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x38, 0x2e, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2e,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x55, 0x6e, 0x69, 0x74, 0x54, 0x79, 0x70,
	0x65, 0x42, 0x0a, 0xfa, 0x42, 0x07, 0x82, 0x01, 0x04, 0x10, 0x01, 0x20, 0x00, 0x52, 0x08, 0x75,
	0x6e, 0x69, 0x74, 0x54, 0x79, 0x70, 0x65, 0x1a, 0x76, 0x0a, 0x14, 0x43, 0x61, 0x70, 0x61, 0x63,
	0x69, 0x74, 0x79, 0x44, 0x61, 0x74, 0x65, 0x52, 0x61, 0x6e, 0x67, 0x65, 0x44, 0x65, 0x66, 0x12,
	0x30, 0x0a, 0x0a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x09, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x61, 0x74,
	0x65, 0x12, 0x2c, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x74, 0x79, 0x70,
	0x65, 0x2e, 0x44, 0x61, 0x74, 0x65, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x44, 0x61, 0x74, 0x65, 0x42,
	0x05, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x42, 0x8f, 0x01, 0x0a, 0x26, 0x63, 0x6f, 0x6d, 0x2e, 0x6d,
	0x6f, 0x65, 0x67, 0x6f, 0x2e, 0x69, 0x64, 0x6c, 0x2e, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2e,
	0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2e, 0x76,
	0x31, 0x50, 0x01, 0x5a, 0x63, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x4d, 0x6f, 0x65, 0x47, 0x6f, 0x6c, 0x69, 0x62, 0x72, 0x61, 0x72, 0x79, 0x2f, 0x6d, 0x6f, 0x65,
	0x67, 0x6f, 0x2d, 0x61, 0x70, 0x69, 0x2d, 0x64, 0x65, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x6f, 0x75, 0x74, 0x2f, 0x67, 0x6f, 0x2f, 0x6d, 0x6f, 0x65, 0x67, 0x6f, 0x2f,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x73, 0x2f, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x5f, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x2f, 0x76, 0x31, 0x3b, 0x6f, 0x6e, 0x6c, 0x69, 0x6e, 0x65, 0x62,
	0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescOnce sync.Once
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescData = file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDesc
)

func file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescGZIP() []byte {
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescOnce.Do(func() {
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescData = protoimpl.X.CompressGZIP(file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescData)
	})
	return file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDescData
}

var file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_goTypes = []interface{}{
	(*GroomingServiceAvailabilityUpdateDef)(nil),     // 0: moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef
	(*BoardingServiceAvailabilityUpdateDef)(nil),     // 1: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef
	(*DaycareServiceAvailabilityUpdateDef)(nil),      // 2: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef
	(*EvaluationServiceAvailabilityUpdateDef)(nil),   // 3: moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef
	(*DateRangeDef)(nil),                             // 4: moego.models.online_booking.v1.DateRangeDef
	(*ArrivalPickUpTimeDef)(nil),                     // 5: moego.models.online_booking.v1.ArrivalPickUpTimeDef
	(*TimeRangeDef)(nil),                             // 6: moego.models.online_booking.v1.TimeRangeDef
	(*DayOfWeekTimeRangeDef)(nil),                    // 7: moego.models.online_booking.v1.DayOfWeekTimeRangeDef
	(*DayTimeRangeDef)(nil),                          // 8: moego.models.online_booking.v1.DayTimeRangeDef
	(*LodgingAvailabilityDef)(nil),                   // 9: moego.models.online_booking.v1.LodgingAvailabilityDef
	(*DayTimeRangeDefList)(nil),                      // 10: moego.models.online_booking.v1.DayTimeRangeDefList
	(*CapacityOverrideDef)(nil),                      // 11: moego.models.online_booking.v1.CapacityOverrideDef
	(*CapacityOverrideDef_CapacityDateRangeDef)(nil), // 12: moego.models.online_booking.v1.CapacityOverrideDef.CapacityDateRangeDef
	(AcceptCustomerType)(0),                          // 13: moego.models.online_booking.v1.AcceptCustomerType
	(v1.PetType)(0),                                  // 14: moego.models.customer.v1.PetType
	(DateLimitType)(0),                               // 15: moego.models.online_booking.v1.DateLimitType
	(*date.Date)(nil),                                // 16: google.type.Date
	(ScheduleType)(0),                                // 17: moego.models.online_booking.v1.ScheduleType
	(CapacityOverrideUnitType)(0),                    // 18: moego.models.online_booking.v1.CapacityOverrideUnitType
}
var file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_depIdxs = []int32{
	13, // 0: moego.models.online_booking.v1.GroomingServiceAvailabilityUpdateDef.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	14, // 1: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef.accepted_pet_types:type_name -> moego.models.customer.v1.PetType
	4,  // 2: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef.available_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	5,  // 3: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	13, // 4: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	9,  // 5: moego.models.online_booking.v1.BoardingServiceAvailabilityUpdateDef.lodging_availability:type_name -> moego.models.online_booking.v1.LodgingAvailabilityDef
	14, // 6: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef.accepted_pet_types:type_name -> moego.models.customer.v1.PetType
	4,  // 7: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef.available_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	5,  // 8: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	13, // 9: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef.accept_customer_type:type_name -> moego.models.online_booking.v1.AcceptCustomerType
	9,  // 10: moego.models.online_booking.v1.DaycareServiceAvailabilityUpdateDef.lodging_availability:type_name -> moego.models.online_booking.v1.LodgingAvailabilityDef
	4,  // 11: moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef.available_date_range:type_name -> moego.models.online_booking.v1.DateRangeDef
	5,  // 12: moego.models.online_booking.v1.EvaluationServiceAvailabilityUpdateDef.arrival_pick_up_time_range:type_name -> moego.models.online_booking.v1.ArrivalPickUpTimeDef
	15, // 13: moego.models.online_booking.v1.DateRangeDef.start_date_type:type_name -> moego.models.online_booking.v1.DateLimitType
	16, // 14: moego.models.online_booking.v1.DateRangeDef.specific_start_date:type_name -> google.type.Date
	15, // 15: moego.models.online_booking.v1.DateRangeDef.end_date_type:type_name -> moego.models.online_booking.v1.DateLimitType
	16, // 16: moego.models.online_booking.v1.DateRangeDef.specific_end_date:type_name -> google.type.Date
	16, // 17: moego.models.online_booking.v1.ArrivalPickUpTimeDef.start_date:type_name -> google.type.Date
	16, // 18: moego.models.online_booking.v1.ArrivalPickUpTimeDef.end_date:type_name -> google.type.Date
	17, // 19: moego.models.online_booking.v1.ArrivalPickUpTimeDef.schedule_type:type_name -> moego.models.online_booking.v1.ScheduleType
	6,  // 20: moego.models.online_booking.v1.ArrivalPickUpTimeDef.arrival_time_range:type_name -> moego.models.online_booking.v1.TimeRangeDef
	6,  // 21: moego.models.online_booking.v1.ArrivalPickUpTimeDef.pick_up_time_range:type_name -> moego.models.online_booking.v1.TimeRangeDef
	7,  // 22: moego.models.online_booking.v1.TimeRangeDef.first_week:type_name -> moego.models.online_booking.v1.DayOfWeekTimeRangeDef
	7,  // 23: moego.models.online_booking.v1.TimeRangeDef.second_week:type_name -> moego.models.online_booking.v1.DayOfWeekTimeRangeDef
	7,  // 24: moego.models.online_booking.v1.TimeRangeDef.third_week:type_name -> moego.models.online_booking.v1.DayOfWeekTimeRangeDef
	7,  // 25: moego.models.online_booking.v1.TimeRangeDef.forth_week:type_name -> moego.models.online_booking.v1.DayOfWeekTimeRangeDef
	8,  // 26: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.monday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 27: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.tuesday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 28: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.wednesday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 29: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.thursday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 30: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.friday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 31: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.saturday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 32: moego.models.online_booking.v1.DayOfWeekTimeRangeDef.sunday:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	8,  // 33: moego.models.online_booking.v1.DayTimeRangeDefList.values:type_name -> moego.models.online_booking.v1.DayTimeRangeDef
	12, // 34: moego.models.online_booking.v1.CapacityOverrideDef.date_ranges:type_name -> moego.models.online_booking.v1.CapacityOverrideDef.CapacityDateRangeDef
	18, // 35: moego.models.online_booking.v1.CapacityOverrideDef.unit_type:type_name -> moego.models.online_booking.v1.CapacityOverrideUnitType
	16, // 36: moego.models.online_booking.v1.CapacityOverrideDef.CapacityDateRangeDef.start_date:type_name -> google.type.Date
	16, // 37: moego.models.online_booking.v1.CapacityOverrideDef.CapacityDateRangeDef.end_date:type_name -> google.type.Date
	38, // [38:38] is the sub-list for method output_type
	38, // [38:38] is the sub-list for method input_type
	38, // [38:38] is the sub-list for extension type_name
	38, // [38:38] is the sub-list for extension extendee
	0,  // [0:38] is the sub-list for field type_name
}

func init() { file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_init() }
func file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_init() {
	if File_moego_models_online_booking_v1_ob_availability_setting_defs_proto != nil {
		return
	}
	file_moego_models_online_booking_v1_ob_availability_setting_enums_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GroomingServiceAvailabilityUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BoardingServiceAvailabilityUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DaycareServiceAvailabilityUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EvaluationServiceAvailabilityUpdateDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DateRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ArrivalPickUpTimeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TimeRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DayOfWeekTimeRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DayTimeRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LodgingAvailabilityDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DayTimeRangeDefList); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapacityOverrideDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapacityOverrideDef_CapacityDateRangeDef); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[1].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[2].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[4].OneofWrappers = []interface{}{
		(*DateRangeDef_SpecificStartDate)(nil),
		(*DateRangeDef_MaxStartDateOffset)(nil),
		(*DateRangeDef_SpecificEndDate)(nil),
		(*DateRangeDef_MaxEndDateOffset)(nil),
	}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[5].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[6].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[8].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[9].OneofWrappers = []interface{}{}
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes[11].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_goTypes,
		DependencyIndexes: file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_depIdxs,
		MessageInfos:      file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_msgTypes,
	}.Build()
	File_moego_models_online_booking_v1_ob_availability_setting_defs_proto = out.File
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_rawDesc = nil
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_goTypes = nil
	file_moego_models_online_booking_v1_ob_availability_setting_defs_proto_depIdxs = nil
}
