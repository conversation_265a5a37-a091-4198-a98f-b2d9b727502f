syntax = "proto3";

package moego.service.offering.v1;

import "moego/models/offering/v1/customize_care_type_model.proto";
import "moego/models/offering/v1/service_enum.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1;offeringsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.offering.v1";

// update care type name request
message UpdateCareTypeNameRequest {
  // care type id
  int64 id = 1 [(validate.rules).int64.gt = 0];
  // care type name
  string name = 2 [(validate.rules).string = {
    min_len: 1
    max_len: 255
  }];
  // company id
  int64 company_id = 3 [(validate.rules).int64.gt = 0];
  // staff id
  int64 staff_id = 4 [(validate.rules).int64.gt = 0];
}

// update care type name response
message UpdateCareTypeNameResponse {}

// list care types request
message ListCareTypesRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // service item type
  repeated moego.models.offering.v1.ServiceItemType service_item_type = 2;
  // staff id
  int64 staff_id = 3;
  // is allow_boarding_and_daycare
  bool is_allow_boarding_and_daycare = 4;
  // is allow_dog_walking
  bool is_allow_dog_walking = 5;
  // is allow_group_class
  bool is_allow_group_class = 6;
}

// list care types response
message ListCareTypesResponse {
  // care types
  repeated models.offering.v1.CustomizeCareTypeView care_types = 1;
}

// customize care type service
service CustomizeCareTypeService {
  // update care type name
  rpc UpdateCareTypeName(UpdateCareTypeNameRequest) returns (UpdateCareTypeNameResponse);
  // list care types
  rpc ListCareTypes(ListCareTypesRequest) returns (ListCareTypesResponse);
}
