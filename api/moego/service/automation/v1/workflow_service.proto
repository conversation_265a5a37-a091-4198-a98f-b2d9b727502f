syntax = "proto3";

package moego.service.automation.v1;

import "moego/models/automation/v1/filter.proto";
import "moego/models/automation/v1/workflow.proto";
import "moego/models/automation/v1/workflow_defs.proto";
import "moego/models/business_customer/v1/business_customer_models.proto";
import "moego/models/enterprise/v1/tenant_models.proto";
import "moego/models/reporting/v2/common_model.proto";
import "moego/models/reporting/v2/filter_model.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/automation/v1;automationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.automation.v1";

// Workflow Service
service WorkflowService {
  // GetWorkflowConfig
  rpc GetWorkflowConfig(GetWorkflowConfigRequest) returns (GetWorkflowConfigResponse);

  // CreateWorkflow
  rpc CreateWorkflow(CreateWorkflowRequest) returns (CreateWorkflowResponse);

  // UpdateWorkflowContent
  rpc UpdateWorkflowContent(UpdateWorkflowContentRequest) returns (UpdateWorkflowContentResponse);
  // UpdateWorkflowInfo
  rpc UpdateWorkflowInfo(UpdateWorkflowInfoRequest) returns (UpdateWorkflowInfoResponse);

  // ListWorkflowCategories
  rpc ListWorkflowCategories(ListWorkflowCategoriesRequest) returns (ListWorkflowCategoriesResponse);
  // ListWorkflows
  rpc ListWorkflows(ListWorkflowsRequest) returns (ListWorkflowsResponse);
  // ListEnterpriseWorkflows
  rpc ListEnterpriseWorkflows(ListEnterpriseWorkflowsRequest) returns (ListEnterpriseWorkflowsResponse);
  // ListWorkflowRecords
  rpc ListWorkflowRecords(ListWorkflowRecordsRequest) returns (ListWorkflowRecordsResponse);
  // ListWorkflowTemplates
  rpc ListWorkflowTemplates(ListWorkflowTemplatesRequest) returns (ListWorkflowTemplatesResponse);
  // GetWorkflowInfo
  rpc GetWorkflowInfo(GetWorkflowInfoRequest) returns (GetWorkflowInfoResponse);
  // GetWorkflowTemplateInfo
  rpc GetWorkflowTemplateInfo(GetWorkflowTemplateInfoRequest) returns (GetWorkflowTemplateInfoResponse);

  // UpdateWorkflowSetting
  rpc UpdateWorkflowSetting(UpdateWorkflowSettingRequest) returns (UpdateWorkflowSettingResponse);

  // GetWorkflowSetting
  rpc GetWorkflowSetting(GetWorkflowSettingRequest) returns (GetWorkflowSettingResponse);

  // opt:CreateWorkflowTemplate
  rpc CreateWorkflowTemplate(CreateWorkflowTemplateRequest) returns (CreateWorkflowTemplateResponse);
  // opt:FilterCustomer
  rpc FilterCustomer(FilterCustomerRequest) returns (FilterCustomerResponse);
  // opt:CopyCompanyWorkflow
  rpc CopyCompanyWorkflow(CopyCompanyWorkflowRequest) returns (CopyCompanyWorkflowResponse);
  // push workflows to companies
  rpc PushWorkflows(PushWorkflowsRequest) returns (PushWorkflowsResponse);
}

// GetWorkflowConfigRequest
message GetWorkflowConfigRequest {}

// GetWorkflowConfigResponse
message GetWorkflowConfigResponse {
  // WorkflowConfig List
  repeated models.automation.v1.WorkflowConfig workflow_configs = 1;
  // Common Filters
  repeated models.reporting.v2.FilterGroup filter_groups = 2;
  // Event Filters
  repeated models.automation.v1.EventFilterGroups event_filter_groups = 3;
}

// CreateWorkflowRequest
message CreateWorkflowRequest {
  // Workflow to be created
  models.automation.v1.CreateWorkflowDef workflow = 1;
}

// CreateWorkflowResponse
message CreateWorkflowResponse {
  // Created workflow
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowContentRequest
message UpdateWorkflowContentRequest {
  // Workflow ID
  int64 workflow_id = 1;
  // Steps to update
  repeated models.automation.v1.CreateStepDef steps = 2;
  // Workflow Consumer Data
  models.automation.v1.Workflow.ConsumerData consumer_data = 3;
}

// UpdateWorkflowContentResponse
message UpdateWorkflowContentResponse {
  // Updated workflow
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowInfoRequest
message UpdateWorkflowInfoRequest {
  // Workflow ID
  int64 workflow_id = 1;
  // Workflow name
  optional string name = 2;
  // Workflow description
  optional string desc = 3;
  // Workflow status
  optional models.automation.v1.Workflow.Status status = 4;
  // Workflow setting
  optional models.automation.v1.WorkflowSetting setting = 5;
  // enterprise apply to
  optional models.automation.v1.WorkflowEnterpriseApply workflow_enterprise_apply = 6;
  // shut down steps option for set workflow to INACTIVE
  optional bool shut_down_pending_steps = 7;
}

// UpdateWorkflowInfoResponse
message UpdateWorkflowInfoResponse {
  // Updated workflow
  models.automation.v1.Workflow workflow = 1;
}

// ListWorkflowCategoriesRequest
message ListWorkflowCategoriesRequest {}

// ListWorkflowCategoriesResponse
message ListWorkflowCategoriesResponse {
  // Workflow categories
  repeated models.automation.v1.WorkflowCategory categories = 1;
}

// ListWorkflowsRequest
message ListWorkflowsRequest {
  // Filter
  message Filter {
    // Workflow name filter
    optional string name = 1;
    // Workflow status filter
    repeated models.automation.v1.Workflow.Status status = 2;
    // Category ID filter
    optional int64 category_id = 3;
  }
  // Pagination request
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter criteria
  optional Filter filter = 2;
}

// ListWorkflowsResponse
message ListWorkflowsResponse {
  // List of workflows
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination response
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// ListEnterpriseWorkflowsRequest
message ListEnterpriseWorkflowsRequest {
  // Filter
  message Filter {
    // Workflow name filter
    optional string name = 1;
    // Workflow status filter
    repeated models.automation.v1.Workflow.Status status = 2;
    // Tenant Group IDs filter
    repeated int64 tenants_group_ids = 3;
    // Tenant IDs filter
    repeated int64 tenants_ids = 4;
  }
  // Pagination request
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter criteria
  optional Filter filter = 2;
}

// ListEnterpriseWorkflowsResponse
message ListEnterpriseWorkflowsResponse {
  // List of workflows
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination response
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// ListWorkflowRecordsRequest
message ListWorkflowRecordsRequest {
  // Filter
  message Filter {
    // Customer name filter
    optional string customer_name = 1;
  }
  // Workflow ID
  int64 workflow_id = 1;
  // Pagination request
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // Filter criteria
  optional Filter filter = 3;
}

// ListWorkflowRecordsResponse
message ListWorkflowRecordsResponse {
  // List of workflow records
  repeated models.automation.v1.WorkflowRecord workflow_records = 1;
  // Pagination response
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// ListWorkflowTemplatesRequest
message ListWorkflowTemplatesRequest {
  // Filter
  message Filter {
    // Template name filter
    optional string name = 1;
    // Category ID filter
    optional int64 category_id = 2;
    // Recommendation type filter
    optional moego.models.automation.v1.Workflow.RecommendType recommend_type = 3;
  }
  // Pagination request
  optional moego.utils.v2.PaginationRequest pagination = 1;
  // Filter criteria
  optional Filter filter = 2;
}

// ListWorkflowTemplatesResponse
message ListWorkflowTemplatesResponse {
  // List of workflow templates
  repeated models.automation.v1.Workflow workflows = 1;
  // Pagination response
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// GetWorkflowInfoRequest
message GetWorkflowInfoRequest {
  // Workflow ID
  int64 workflow_id = 1;
}

// GetWorkflowInfoResponse
message GetWorkflowInfoResponse {
  // Workflow information
  models.automation.v1.Workflow workflow = 1;
}

// GetWorkflowTemplateInfoRequest
message GetWorkflowTemplateInfoRequest {
  // Workflow template ID
  int64 workflow_id = 1;
}

// GetWorkflowTemplateInfoResponse
message GetWorkflowTemplateInfoResponse {
  // Workflow template information
  models.automation.v1.Workflow workflow = 1;
}

// CreateWorkflowTemplateRequest
message CreateWorkflowTemplateRequest {
  // Workflow template to be created
  models.automation.v1.CreateWorkflowDef workflow = 1;
}

// CreateWorkflowTemplateResponse
message CreateWorkflowTemplateResponse {
  // Created workflow template
  models.automation.v1.Workflow workflow = 1;
}

// UpdateWorkflowSettingRequest
message UpdateWorkflowSettingRequest {
  // Company ID
  int64 company_id = 1;
  // Workflow setting
  models.automation.v1.WorkflowSetting setting = 2;
}

// UpdateWorkflowSettingResponse
message UpdateWorkflowSettingResponse {
  // Updated workflow setting
  models.automation.v1.WorkflowSetting setting = 1;
}

// GetWorkflowSettingRequest
message GetWorkflowSettingRequest {
  // Company ID
  int64 company_id = 1;
}

// GetWorkflowSettingResponse
message GetWorkflowSettingResponse {
  // Workflow setting
  models.automation.v1.WorkflowSetting setting = 1;
}

// FilterCustomerRequest
message FilterCustomerRequest {
  // Filter requests
  repeated models.reporting.v2.FilterRequest filters = 1;
  // Pagination request
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // Company ID
  int64 company_id = 3;
  // Tenants IDs, empty is all tenants
  repeated int64 tenants_ids = 4;

  // Customer ID filter
  optional int64 customer_id = 10;
}

// FilterCustomerResponse
message FilterCustomerResponse {
  // List of customers
  repeated moego.models.business_customer.v1.BusinessCustomerInfoModel customer = 1;
  // Pagination response
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// CopyCompanyWorkflowRequest
message CopyCompanyWorkflowRequest {
  // Source Company ID
  int64 source_company_id = 1;
  // Target Company ID
  int64 target_company_id = 2;
}

// CopyCompanyWorkflowResponse
message CopyCompanyWorkflowResponse {}

// PushWorkflowsRequest
message PushWorkflowsRequest {
  // Workflow IDs to push
  repeated int64 ids = 1 [(validate.rules).repeated = {
    min_items: 1
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // tenant objects to push to
  repeated models.enterprise.v1.TenantObject targets = 2 [(validate.rules).repeated = {min_items: 1}];
}

// PushWorkflowsResponse
message PushWorkflowsResponse {}
