syntax = "proto3";

package moego.models.payment.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// buf:lint:ignore ENUM_ZERO_VALUE_SUFFIX
// tip price type
enum TipPriceType {
  // tip price type
  TIP_PRICE_TYPE_AMOUNT = 0;
  // tip amount type
  TIP_PRICE_TYPE_PERCENT = 1;
}

// preferred tip type
enum PreferredTipType {
  // unspecified
  PREFERRED_TIP_TYPE_UNSPECIFIED = 0;
  // low
  PREFERRED_TIP_TYPE_LOW = 1;
  // medium
  PREFERRED_TIP_TYPE_MEDIUM = 2;
  // high
  PREFERRED_TIP_TYPE_HIGH = 3;
}
