syntax = "proto3";

package moego.models.payment.v1;

import "google/type/money.proto";
import "moego/models/payment/v1/payment_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v1;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v1";

// Refund Payment. 直接由 Refund 表现状定义，未整理和加工。
// 与 Payment 对应.
message RefundPaymentModel {
  // ID.
  int64 id = 1;
  // Module.
  string module = 2;
  // Invoice ID.
  int64 invoice_id = 3;
  // Refund ID. Useless.
  int64 refund_id = 4;
  // Customer ID.
  int64 customer_id = 5;
  // Staff ID.
  int64 staff_id = 6;
  // Method Name.
  string method = 7;
  // Amount.
  google.type.Money amount = 8;
  // Status.
  RefundPaymentStatus status = 9;
  // Create time.
  int64 create_time = 10;
  // Update Time.
  int64 update_time = 11;
  // Stripe refund ID.
  string stripe_refund_id = 12;
  // Origin payment ID.
  int64 origin_payment_id = 13;
  // Method ID.
  int64 method_id = 14;
  // Business ID.
  int64 business_id = 15;
  // Reason.
  string reason = 16;
  // Error.
  string error = 17;
  // Source payment ID.
  string source_payment_id = 18;
  // Grooming ID.
  uint64 grooming_id = 19;
  // Company ID.
  int64 company_id = 20;
  // Booking fee.
  google.type.Money booking_fee = 21;

  // Currency code.
  string currency_code = 22;
  // Refund order payment ID.
  int64 refund_order_payment_id = 23;
}
