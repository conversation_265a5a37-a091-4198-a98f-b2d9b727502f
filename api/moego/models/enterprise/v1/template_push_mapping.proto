syntax = "proto3";

package moego.models.enterprise.v1;

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/enterprise/v1;enterprisepb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.enterprise.v1";

// TemplateType
enum TemplateType {
  // Unspecified template type
  TEMPLATE_TYPE_UNSPECIFIED = 0;
  // service
  SERVICE = 1;
  // workflow
  WORKFLOW = 2;
}
