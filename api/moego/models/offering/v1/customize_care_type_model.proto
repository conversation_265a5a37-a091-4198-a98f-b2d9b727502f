syntax = "proto3";

package moego.models.offering.v1;

import "google/protobuf/timestamp.proto";
import "moego/models/offering/v1/service_enum.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.offering.v1";

// The customize care type model
message CustomizeCareTypeModel {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // company id
  int64 company_id = 3;
  // service type
  ServiceItemType service_item_type = 4;
  // create time
  google.protobuf.Timestamp create_at = 5;
  // update time
  google.protobuf.Timestamp update_at = 6;
  // delete time
  google.protobuf.Timestamp delete_at = 7;
}

// The customize care type view
message CustomizeCareTypeView {
  // id
  int64 id = 1;
  // name
  string name = 2;
  // service item type
  ServiceItemType service_item_type = 3;
  // sort
  int32 sort = 4;
}
