syntax = "proto3";

package moego.client.business_customer.v1;

import "moego/models/business_customer/v1/business_pet_feeding_schedule_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/business_customer/v1;businesscustomerapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.business_customer.v1";

// List pet's feeding schedule params
message ListPetFeedingScheduleParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // pet ids
  repeated int64 pet_ids = 3 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      int64: {gt: 0}
    }
    unique: true
  }];
}

// List pet's feeding schedule result
message ListPetFeedingScheduleResult {
  // pet feeding schedules
  repeated PetFeedingSchedule pet_feeding_schedules = 1;

  // pet feeding schedules
  message PetFeedingSchedule {
    // pet id
    int64 pet_id = 1;
    // pet's feeding schedules
    repeated models.business_customer.v1.BusinessPetFeedingScheduleView feeding_schedules = 2;
  }
}

// Business pet feeding schedule service
service BusinessPetFeedingScheduleService {
  // List pet's feeding schedule
  rpc ListPetFeedingSchedule(ListPetFeedingScheduleParams) returns (ListPetFeedingScheduleResult);
}
