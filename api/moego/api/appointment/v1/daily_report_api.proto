// @since 2024-06-24 10:14:35
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.api.appointment.v1;

import "google/type/date.proto";
import "moego/models/appointment/v1/daily_report_defs.proto";
import "moego/models/appointment/v1/daily_report_enums.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1;appointmentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.appointment.v1";

// get daily report config params
message GetDailyReportConfigParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 3 [(validate.rules).message = {required: true}];
}

// get daily report config params
message GetDailyReportConfigResult {
  // the id
  int64 id = 1;
  // report
  models.appointment.v1.ReportDef report = 2;
  // report card status
  moego.models.appointment.v1.ReportCardStatus status = 3;
}

// get daily report config params
message GetDailyReportSentResultParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 2 [(validate.rules).message = {required: true}];
}

// get daily report config params
message GetDailyReportSentResultResult {
  // sent results
  repeated models.appointment.v1.SentResultDef sent_results = 1;
}

// create daily report config params
message UpsertDailyReportConfigParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // customer id
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 4 [(validate.rules).message = {required: true}];
  // report
  models.appointment.v1.ReportDef report = 5 [(validate.rules).message = {required: true}];
}

// get daily report config params
message UpsertDailyReportConfigResult {
  // the id
  int64 id = 1;
  // uuid
  string uuid = 2;
}

// get daily report sent history params
message GetDailyReportSentHistoryParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// get daily report sent history result
message GetDailyReportSentHistoryResult {
  // sent history list
  repeated models.appointment.v1.SentHistoryRecordDef sent_history_records = 1;
}

// get daily report for customer params
message GetDailyReportForCustomerParams {
  // uuid
  string uuid = 1 [(validate.rules).string = {max_len: 50}];
}

// get daily report for customer result
message GetDailyReportForCustomerResult {
  // report
  models.appointment.v1.ReportDef report = 1;
  // service date
  google.type.Date service_date = 2;
  // pet
  Pet pet = 3;
  // pet
  message Pet {
    // pet id
    int64 pet_id = 1;
    // pet name
    string pet_name = 2;
    // pet avatar
    string avatar_path = 3;
    // pet type
    models.customer.v1.PetType pet_type = 4;
  }
  // business
  Business business = 4;
  // business
  message Business {
    // business id
    int64 business_id = 1;
    // business name
    string business_name = 2;
    // business avatar
    string avatar_path = 3;
  }
}

// generate message params
message GenerateMessageContentParams {
  // appointment id
  int64 appointment_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet id
  int64 pet_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service date
  google.type.Date service_date = 3 [(validate.rules).message = {required: true}];
}

// generate message result
message GenerateMessageContentResult {
  // message for sending
  string message = 1;
}

// send message params
message SendMessageParams {
  // id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // send method (default is SMS)
  optional moego.models.appointment.v1.SendMethod send_method = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
  // recipient emails
  repeated string recipient_emails = 3 [(validate.rules).repeated = {
    unique: true
    items: {
      string: {
        email: true
        min_len: 1
      }
    }
  }];
}

// send message result
message SendMessageResult {
  // result
  bool result = 1;
}

// list daily report config params
message ListDailyReportConfigParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // list daily report config def
  models.appointment.v1.ListDailyReportConfigFilter filter = 2 [(validate.rules).message = {required: true}];
  // pagination
  moego.utils.v2.PaginationRequest pagination = 3 [(validate.rules).message = {required: true}];
}

// list daily report config result
message ListDailyReportConfigResult {
  // report list
  repeated models.appointment.v1.DailyReportConfigDef report_configs = 1;
  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
  // pet
  repeated Pet pets = 3;

  // pet
  message Pet {
    // pet id
    int64 pet_id = 1;
    // pet name
    string pet_name = 2;
    // pet avatar
    string avatar_path = 3;
    // pet type
    models.customer.v1.PetType pet_type = 4;
  }
}

// batch send daily draft report params
message BatchSendDailyDraftReportParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // daily report ids
  repeated int64 daily_report_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // send method
  moego.models.appointment.v1.SendMethod send_method = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: 0
  }];
}

// batch send daily draft report result
message BatchSendDailyDraftReportResult {}

// batch delete daily report config params
message BatchDeleteDailyReportConfigParams {
  // business id
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // daily report ids
  repeated int64 daily_report_ids = 2 [(validate.rules).repeated = {
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
}

// batch delete daily report config result
message BatchDeleteDailyReportConfigResult {}

// the daily report service
service DailyReportService {
  // get daily report config
  rpc GetDailyReportConfig(GetDailyReportConfigParams) returns (GetDailyReportConfigResult);
  // get daily report config sent result
  rpc GetDailyReportSentResult(GetDailyReportSentResultParams) returns (GetDailyReportSentResultResult);
  // upsert daily report config
  rpc UpsertDailyReportConfig(UpsertDailyReportConfigParams) returns (UpsertDailyReportConfigResult);
  // get daily report sent history
  rpc GetDailyReportSentHistory(GetDailyReportSentHistoryParams) returns (GetDailyReportSentHistoryResult);
  // get daily report for customer
  rpc GetDailyReportForCustomer(GetDailyReportForCustomerParams) returns (GetDailyReportForCustomerResult);
  // generate message content
  rpc GenerateMessageContent(GenerateMessageContentParams) returns (GenerateMessageContentResult);
  // send message
  rpc SendMessage(SendMessageParams) returns (SendMessageResult);
  // get daily report list
  rpc ListDailyReportConfig(ListDailyReportConfigParams) returns (ListDailyReportConfigResult);
  // batch send daily draft report
  rpc BatchSendDailyDraftReport(BatchSendDailyDraftReportParams) returns (BatchSendDailyDraftReportResult);
  // batch delete daily report config
  rpc BatchDeleteDailyReportConfig(BatchDeleteDailyReportConfigParams) returns (BatchDeleteDailyReportConfigResult);
}
