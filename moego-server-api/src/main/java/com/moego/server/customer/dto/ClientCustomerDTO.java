package com.moego.server.customer.dto;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import lombok.Data;

@Data
public class ClientCustomerDTO {

    private SessionInfo session;
    private AccountInfo account;

    @SuppressFBWarnings("UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD")
    public static class SessionInfo {

        public String token;
    }

    @SuppressFBWarnings("UUF_UNUSED_PUBLIC_OR_PROTECTED_FIELD")
    public static class AccountInfo {

        public Integer id;
        public String email;
        public String phoneNumber;
        public Integer status;
        public String firstName;
        public String lastName;
        public String avatarPath;
        //    private String timezoneName;
        //    private Integer timezoneSeconds;
        //    private String country;
        //    private Integer createTime;
        //    private Integer updateTime;
    }
}
