package com.moego.server.customer.api;

import com.moego.server.customer.dto.CustomerPetVaccineDto;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.customer.dto.VaccineStatusDto;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IPetVaccineService {
    /**
     * DONE(account structure): businessId 没有用到
     *
     * 获取 宠物疫苗的过期状态
     *
     * @param petIdList
     * @return
     */
    @PostMapping("/service/customer/petVaccine/getVaccineStatusByPetIdList")
    Map<Integer, VaccineStatusDto> getVaccineStatusByPetIdList(
            @RequestParam("businessId") Integer businessId, @RequestBody List<Integer> petIdList);

    /**
     * DONE(account structure): 无需修改
     */
    @GetMapping("/service/customer/petVaccine/getVaccineStatusByPetIdList")
    List<CustomerPetVaccineDto> getVaccineByCustomerId(@RequestParam("customerId") Integer customerId);

    /**
     * DONE(account structure): 无需修改
     */
    @PostMapping("/service/customer/petVaccine/getVaccineInfoByPetIdList")
    Map<Integer, List<VaccineBindingRecordDto>> getVaccineInfoByPetIdList(@RequestBody List<Integer> petIdList);
}
