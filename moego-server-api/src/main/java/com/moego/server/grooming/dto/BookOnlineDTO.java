package com.moego.server.grooming.dto;

import com.moego.common.enums.BooleanEnum;
import com.moego.idl.models.offering.v1.ServiceItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class BookOnlineDTO {
    private Integer id;
    private Integer businessId;
    private Byte isEnable;
    private Integer maxAvailableDist;
    private Integer maxAvailableTime;
    private Integer soonestAvailable;
    private Integer farestAvailable;
    private Long createTime;
    private Long updateTime;
    private Byte isRequireAgreement;
    private String zipCode;
    private String placeName;
    private String state;
    private String stateAbbreviation;
    private String county;
    private Byte isNeedAddress;
    private Byte isNeedSelectTime;
    private Byte fakeIt;
    /**
     * @see com.moego.idl.models.online_booking.v1.PaymentType
     */
    private Byte enableNoShowFee;

    private BigDecimal noShowFee;
    private Integer appointmentInterval;
    private Integer timeslotMins;
    private Byte timeslotFormat;
    private Byte acceptClient;
    private Byte autoMoveWait;
    private Byte autoRefundDeposit;
    private Byte serviceAreaEnable;
    private Byte weightLimitNotify;
    private Integer weightLimit;
    private String overLimitTips;
    private Byte needWithinArea;
    private Byte isByZipcode;
    private Byte isByRadius;
    private String settingLocation;
    private String settingLat;
    private String settingLng;
    private Byte isCheckExistingClient;
    private Byte isRedirect;
    private Byte autoAccept;
    private Byte showOneAvailableTime;
    private Byte smartScheduleEnable;
    private Integer smartScheduleMaxDist;
    private Integer smartScheduleMaxTime;
    private String zipCodes;
    private List<Integer> serviceAreas;
    private String bookOnlineName;
    private Byte allowedSimplifySubmit;
    private Byte availableTimeType;
    private Byte bySlotTimeslotFormat;
    private Integer bySlotTimeslotMins;
    private Integer bySlotSoonestAvailable;
    private Integer bySlotFarthestAvailable;
    private Byte serviceFilter;
    private Boolean isShowCategories;
    private Byte prepayType;
    private Byte prepayTipEnable;
    private Byte depositType;
    private Integer depositPercentage;
    private BigDecimal depositAmount;

    private Byte useVersion;
    private Byte preAuthTipEnable;
    private String requestSubmittedAutoType;
    private Boolean displayStaffSelectionPage;
    private Integer arrivalWindowBeforeMin;
    private Integer arrivalWindowAfterMin;
    private Integer bookingRangeStartOffset;
    private Byte bookingRangeEndType;
    private Integer bookingRangeEndOffset;
    private String bookingRangeEndDate;
    private Boolean isNeedSendRenewNotification;
    private String cancellationPolicy;
    private String description;
    private String prepayPolicy;
    private String preAuthPolicy;
    private Long companyId;
    private Boolean availableTimeSync;

    private Byte groupAcceptClient;
    private String groupFilterRule;
    private Byte groupPaymentType;
    private Byte groupPrepayType;
    private Byte groupPrepayTipEnable;
    private Byte groupDepositType;
    private Integer groupDepositPercentage;
    private BigDecimal groupDepositAmount;
    private Byte groupPreAuthTipEnable;
    private String groupCancellationPolicy;
    private String groupPrepayPolicy;
    private String groupPreAuthPolicy;

    private Boolean bySlotShowOneAvailableTime;

    @Nullable
    public Byte getAvailableTimeSync() {
        if (availableTimeSync == null) {
            return null;
        }
        return availableTimeSync ? BooleanEnum.VALUE_TRUE : BooleanEnum.VALUE_FALSE;
    }

    public void setAvailableTimeSync(@Nullable Byte availableTimeSync) {
        if (availableTimeSync == null) {
            this.availableTimeSync = null;
        } else {
            this.availableTimeSync = BooleanEnum.VALUE_TRUE.equals(availableTimeSync);
        }
    }

    /**
     * 商家定制字段，假装这个字段不存在，专门为 Red Dog 商家做的需求
     *
     * <p> key 为 {@link ServiceItemType}
     *
     * @see ServiceItemType
     */
    private Map<Integer, PaymentOption> paymentOptionMap;

    public interface BookingRangeEndType {
        Byte USING_OFFSET = 1;
        Byte USING_DATE = 2;
    }

    public interface TimeslotFormat {
        Byte EXACT_TIME = 1;
        Byte ARRIVAL_WINDOW = 2;
        Byte DATE_ONLY = 3;
    }

    public interface AvailableTimeType {
        Byte BY_WORKING_HOURS = 0;
        Byte BY_SLOT = 1;
        Byte DISABLED = 2;
    }

    public interface UseVersion {
        Byte OB_2_0 = 1;
        Byte OB_3_0 = 2;
    }

    @Data
    public static class PaymentOption {
        /**
         * @see com.moego.idl.models.online_booking.v1.PaymentType
         */
        @Schema(description = "see moego.models.online_booking.v1.PaymentType")
        private Integer paymentType;

        @Schema(description = "当 paymentType 为 PREPAY 时，才会使用到 prePay")
        private PrePay prePay;

        @Data
        public static class PrePay {
            /**
             * @see com.moego.idl.models.online_booking.v1.PrepayType
             */
            @Schema(description = "see moego.models.online_booking.v1.PrepayType")
            private Integer prepayType;
            /**
             * @see com.moego.idl.models.online_booking.v1.PrepayDepositType
             */
            @Schema(description = "see moego.models.online_booking.v1.PrepayDepositType")
            private Integer depositType;

            private Integer depositPercentage;
            private BigDecimal depositAmount;
        }
    }
}
