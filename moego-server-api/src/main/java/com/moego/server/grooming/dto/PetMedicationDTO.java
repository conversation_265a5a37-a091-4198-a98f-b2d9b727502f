package com.moego.server.grooming.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class PetMedicationDTO {
    private String medicationAmount;
    private String medicationUnit;
    private String medicationName;
    private String medicationNote;
    private List<PetScheduleSettingDTO> medicationSchedules;

    @Schema(description = "Selected date for medication, default is everyday include checkout day")
    private SelectedDate selectedDate;

    @Data
    public static class SelectedDate {
        Integer dateType;
        List<String> specificDates;
    }
}
