package com.moego.server.business.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeStaffsParams(
        Set<Integer> ids,
        Set<Long> companyIds,
        Set<Integer> businessIds,
        Set<Integer> accountIds,
        Set<Integer> roleIds,
        Byte employeeCategory,
        boolean includeDeleted,
        String name,
        @NotNull @Valid Pagination pagination) {}
