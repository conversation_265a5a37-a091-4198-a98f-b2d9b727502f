package com.moego.server.business.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class StaffPayrollSettingParams {

    private Integer id;

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @NotNull
    @Min(1)
    private Integer staffId;

    // enable switch
    @Schema(description = "service commission 开关")
    private Boolean serviceCommissionEnable;

    @Schema(description = "hourly commission 开关")
    private Boolean hourlyCommissionEnable;

    @Schema(description = "tips commission 开关")
    private Boolean tipsCommissionEnable;

    @Schema(description = "service commission 类型: 1-fixed rate, 2-tier rate")
    @Max(2)
    @Min(1)
    private Byte serviceCommissionType;

    // fixed rate
    @Schema(description = "service rate, 百分比, 2位小数")
    @DecimalMax(value = "100")
    @DecimalMin(value = "0")
    private BigDecimal servicePayRate;

    @Schema(description = "addon rate, 百分比, 2位小数")
    @DecimalMax(value = "100")
    @DecimalMin(value = "0")
    private BigDecimal addonPayRate;

    // tier rate
    @Schema(description = "tier 计算类型: 1-sliding scale, 2-progressive")
    @Max(2)
    @Min(1)
    private Byte tierType;

    @Schema(description = "tier rate 配置列表")
    @Valid
    private List<PayrollTierConfigParams> tierConfig;

    // hourly pay
    @Schema(description = "hourly pay 时薪，2位小数")
    @DecimalMin(value = "0")
    private BigDecimal hourlyPay;

    // tips rate
    @Schema(description = "tips rate, 百分比, 2位小数")
    @DecimalMax(value = "100")
    @DecimalMin(value = "0")
    private BigDecimal tipsPayRate;
}
