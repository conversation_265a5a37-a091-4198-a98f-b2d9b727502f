package com.moego.server.payment.dto.stripe;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023/10/24
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class HardwareOrderDetail {
    String id;
    String object;
    Integer amount;
    String currency;
    String livemode;

    String paymentType;

    String poNumber;

    List<Tracking> shipmentTracking;

    String shippingMethod;

    Shipping shipping;
    String status;
    Integer tax;

    List<TaxAmounts> totalTaxAmounts;

    List<OrderItems> hardwareOrderItems;

    Map<String, String> metadata;

    @Data
    public static class Tracking {
        String carrier;
        String trackingNumber;
    }

    @Data
    public static class Address {
        String line1;
        String postalCode;

        String country;

        String city;
        String state;
        String line2;
    }

    @Data
    public static class Shipping {
        Address address;
        Integer amount;
        String company;
        String currency;

        String email;
        String name;
        String phone;
    }

    @Data
    public static class TaxAmounts {
        Integer amount;
        String inclusive;

        @Data
        public static class Rate {
            String displayName;

            String jurisdiction;
            String percentage;
        }
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class OrderItems {
        Integer amount;
        String currency;
        Integer quantity;
        TerminalHardwareSku terminalHardwareSku;
    }
}
