package com.moego.server.payment.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/3/12 5:49 PM
 */
@Data
public class CompanyCardDTO {

    String name;
    String brand;
    String country;
    String cvcCheck;

    @Schema(description = "卡是否有效 true: 有效， false：无效")
    Boolean isValid;

    @Schema(description = "主卡: true is primary, false not primary")
    Boolean isDefault;

    @Schema(description = "stripe generated id")
    String cardId;

    String last4;
    Long expMonth;
    Long expYear;
}
